{"name": "cicada-dataCenter-webui", "version": "3.6.0", "private": true, "scripts": {"start": "vue-cli-service serve --mode dev", "serve": "npm i dc-front-plugin && vue-cli-service serve --mode dev", "build:prod": "node --max_old_space_size=4096 node_modules/@vue/cli-service/bin/vue-cli-service.js build --mode prod", "build:standard": "vue-cli-service build --mode standard", "build:variable": "vue-cli-service build --mode variable", "lint": "eslint src --fix --ext .vue,.js,.jsx"}, "dependencies": {"axios": "^0.19.0", "codemirror": "^5.65.15", "core-js": "^2.6.5", "crypto-js": "^4.0.0", "dayjs": "^1.11.11", "dc-front-plugin": "^1.0.5", "echarts": "^4.9.0", "element-ui": "^2.15.12", "jquery": "^3.4.1", "jquery-ui-dist": "^1.12.1", "js-cookie": "^2.2.1", "jsplumb": "^2.11.2", "mockjs": "^1.1.0", "numerify": "^1.2.9", "panzoom": "^9.4.3", "register-service-worker": "^1.6.2", "sortablejs": "^1.10.0", "sql-formatter": "^4.0.2", "ui-component-v4": "1.2.17", "v-charts": "^1.19.0", "viewerjs": "^1.3.6", "vue": "^2.6.10", "vue-clipboard2": "^0.3.1", "vue-codemirror": "^4.0.6", "vue-context-menu": "^2.0.6", "vue-grid-layout": "^2.3.8", "vue-router": "^3.0.3", "vue-video-player": "^5.0.2", "vuedraggable": "^2.24.3", "vuex": "^3.0.1", "xe-utils": "^2.2.4", "xlsx": "^0.17.4"}, "devDependencies": {"@babel/plugin-proposal-optional-chaining": "^7.21.0", "@vue/babel-preset-app": "^5.0.8", "@vue/cli-plugin-babel": "^3.11.0", "@vue/cli-plugin-pwa": "^3.11.0", "@vue/cli-service": "^3.11.0", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "babel-polyfill": "^6.26.0", "build-info-plugin-webpack": "^1.1.0", "compression-webpack-plugin": "^6.1.1", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "less": "^3.0.4", "less-loader": "^5.0.0", "mini-css-extract-plugin": "^1.6.2", "style-resources-loader": "^1.3.3", "svg-sprite-loader": "^6.0.11", "vue-cli-plugin-style-resources-loader": "^0.1.4", "vue-template-compiler": "^2.6.10", "vue2-ace-editor": "0.0.15", "webpack-bundle-analyzer": "^4.10.2"}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions"]}