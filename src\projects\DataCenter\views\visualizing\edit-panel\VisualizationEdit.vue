<template>
    <div class="visualizationEdit selectn" v-loading="settings.loading">
        <div class="ce-panel_title-bg">
            <div class="ce-panel_title">
                <i class="el-icon-s-data pr6"></i>
                <div class="ce-visual_title" ref="title">
                    <el-input size="mini" @keyup.native="titleChange(title)" :readonly="viewing" maxlength="30"
                              v-model.trim="title"></el-input>
                </div>
                <!-- <input @keyup="titleChange(title)"   class="ce-visual_title" type="text" v-model="title"/>-->
            </div>
            <div class="ce-buttons_group" v-if="!viewing">
                <el-radio-group size="mini" class="ce-radio" @change="stateChange" v-model="state">
                    <el-radio-button
                        v-for="(btn , i) in stateBtns"
                        :key="i"
                        :label="btn.value"
                    >{{ btn.label }}
                    </el-radio-button>
                </el-radio-group>
                <el-button v-for="btn in btns" :key="btn.txt" class="ce-btn" :type="btn.type" size="mini"
                        v-if="btn.show"
                        @click="btn.fn">{{ btn.txt }}
                </el-button>
            </div>
        </div>
        <div class="ce-right-attr" v-show="state === 'edit'" :class="{'ce-attr_hide' : !showAttr}">
            <div class="ce-attr_title" v-show="showAttr">
                <span>{{ attr_title }}<help-document :code="documentCode"></help-document></span>
                <em class="el-icon-close ce-attr_close" :title="attrIcons.close" @click="closeAttrPanel($event)"></em>
                <el-popover
                    ref="popover"
                    trigger="click"
                    v-model="showChangeCharts"
                    v-if="showChangeIcon"
                >
                    <change-plugin :plugins="changePlugins" @changeWidgetMeta="changeWidgetMeta"></change-plugin>
                    <em type="text" slot="reference" :title="attrIcons.changeChart"
                        class="el-icon-sort ce-icon-rotate ce-attr_close"></em>
                </el-popover>

            </div>
            <div v-if="!loading" v-loading="loading">
                <AttrSearch ref="sea_attr" :dir_dataSetId="dir_dataSetId" :nodeList="nodeList" :plug="plug"
                            @getVisualNodes="getVisualNodes" v-if="plug.code === 'SelectWidget'"/>
                <AttrText v-else-if="plug.code === 'TextWidget'"/>
                <AttrTabs ref="tab_attr" :nodeList="nodeList" @getVisualNodes="getVisualNodes"
                          :dir_dataSetId="dir_dataSetId" v-else :plug="plug"/>
            </div>

        </div>
        <div class="ce-left-panel" :class="{'ce-marginL' : showAttr , 'ce-is-full' : isFullScreen}">
            <VisualPlug ref="plug" @getPlugins="getPlugins" :showAttr="showAttr" :isView="viewing"/>
            <VisualPanel ref="visual" :isView="viewing" @showAttrPanel="showAttrPanel" @closeAttr="closeAttrPanel"/>
        </div>
        <SaveDialog ref="saveModel" @reNewRowData="reNewRowData"/>
    </div>
</template>

<script>
import VisualPlug from '../plugin/VisualPlug'
import VisualPanel from '../visual/VisualPanel'
import AttrTabs from '../attribute/AttrTabs'
import AttrText from '../attribute/AttrText'
import AttrSearch from '../attribute/search-attr/index'
import SaveDialog from '../save/SaveDialog'
import {servicesMixins} from "../service-mixins/service-mixins";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {mapGetters} from "vuex";
import {attrMixins} from "../attribute/attr-mixins/attr-mixins";
import ChangePlugin from "../plugin/change-plugin"
import {globalBus} from "@/api/globalBus";
import {common} from "@/api/commonMethods/common";

export default {
    name: "VisualizationEdit",
    mixins: [servicesMixins, commonMixins ,common],
    components: {
        VisualPlug,
        VisualPanel,
        AttrTabs,
        AttrText,
        AttrSearch,
        SaveDialog,
        ChangePlugin
    },
    props :{
        isCase : {
            type : Boolean,
            default : false,
        }
    },
    computed: {
        ...mapGetters(["userInfo"]),
        dir_dataSetId() {
            return this.rowData.dataSetId;
        }
    },
    data() {
        return {
            isFullScreen: false,
            attrIcons: { //属性面板图标按钮title
                close: "关闭",
                changeChart: "切换图表"
            },
            title: '',
            attr_title: '',
            state: 'edit',
            stateBtns: [
                {
                    label: '编辑',
                    value: 'edit'
                }, {
                    label: '预览',
                    value: 'preview'
                },
            ],
            btns: [
                {
                    txt: '保存',
                    type: 'primary',
                    fn: this.save,
                    show : !this.isCase,
                },
                /* {
                     txt :'发布共享' ,
                     type : "primary" ,
                     fn : this.issuePanel
                 },*/
                {
                    txt: '关闭',
                    type: '',
                    fn: this.close,
                    show : true,
                }
            ],

            showAttr: false,
            plug: null,
            loading: true,
            rowData: {},
            viewing: true,
            nodeList: [],
            filterTxt: "",
            groupId: "",
            bkDataset: [],
            clearMsg: "",
            fastCountDatacopy: {},//快速计算
            allDatasetFields: {},//存放所有数据集字段
            makeUpData: {},//补齐时间
            chartsType: ["BarChartWidget", "StackBarChartWidget", "TransverseBarChartWidget", "TransverseStackBarChartWidget", "LineChartWidget", "AreaGraphWidget", "PieChartWidget"],//"支持图表切换"
            showChangeIcon: false,
            changePlugins: [],
            showChangeCharts: false,
            documentCode:'',//帮助文档code
            codeList:{
                "TextWidget":"dashboardContent",
                "SelectWidget":"dashboardQuery",
                "TransverseBarChartWidget":"transverseHistogram",//横向柱状图
                "TransverseStackBarChartWidget":"horizontalStackedHistogram",//横向堆叠柱状图
                "StackBarChartWidget":"stackedColumnChart",//堆叠柱状图
                "BarChartWidget":"basicHistogram",//基本柱状图
                "AreaGraphWidget":"areaMap",//面积图
                "LineChartWidget":"basicLineChart",//基本折线图
                "RingPieChartWidget":"nestedPieChart",//嵌套饼图
                "PieChartWidget":"basicPieChart",//基本饼图
                "TableChartWidget":"dashboardTable",//表格
                "CombinationWidget":"combinationDiagram",//组合图
                "WordCloudWidget":"wordCloudDiagram",//词云图
                "HeatMap":"thermodynamicDiagram",//热力图
                "PGISWidget":"PGISMap",//PGIS地图
                "TabWidget":"dashboardTab",//Tab页签
                "BubbleMap":"bubbleMap",//气泡地图
                "ColourMap":"colorMap",//色彩地图
                "MapChartWidget":"basicMap",//基本地图
                "IndicatorCardWidget":"indexCard",//指标卡
                "RadarWidget":"radarChart",//雷达图
                "RelationshipWidget":"relationDiagram",//关系图
                "FormWidget":"dashboardForm",//表单
                "BusinessRelationshipWidget":"businessRelationshipDiagram",//业务关系图
            }
        }
    },
    methods: {
        /**
         * 切换图表
         *
         * */
        changeWidgetMeta(plug) {
            globalBus.$emit("changeWidgetMeta", this.plug, plug);
        },
        /**
         * 获取可切换的图表 （暂时只有柱状图，折线图，饼图)
         * @param data 图表图标等数据
         * */
        getPlugins(data) {
            if (!data) return;
            const vm = this;
            let charts = [];
            data.forEach(item => {
                if (item.plugList) {
                    item.plugList.forEach(list => {
                        if (list.children) {
                            list.children.forEach(child => {
                                if (vm.chartsType.indexOf(child.code) > -1) {
                                    charts.push(child);
                                }
                            })
                        }
                    })
                }
            })
            vm.changePlugins = charts;
        },
        getMakeUpData: attrMixins.methods.getMakeUpData,
        requestFastCount: attrMixins.methods.requestFastCount,
        async confirmFn(fn) {
            const vm = this;
            vm.$confirm('保存并发布共享?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'info'
            }).then(async () => {
                await fn();
            }).catch(() => {
            });
        },
        async issuePanel() {
            const vm = this;
            this.confirmFn(async () => {
                let req = await vm.save();
                if (req) {
                    vm.$emit('issue', vm.rowData);
                    vm.close();
                }

            })
        },
        getVisualNodes() {
            this.nodeList = this.$refs.visual.nodeList;
        },
        async saveOrUpdateData(nodeList, groupId, name) {
            const vm = this, {visualServices, visualMock, settings} = this;
            let services = vm.getServices(visualServices, visualMock);
            if (name === "") {
                this.$message.warning("仪表盘名称为空");
                return false;
            }
            let dashboard = {
                id: this.rowData ? this.rowData.id : undefined,
                name: name,
                groupId: groupId,
                widgets: [],
                dataSetId: this.rowData.dataSetId,
                isCache: this.rowData.isCache,
                cron: this.rowData.cron,
            };
            for (let i = 0; i < nodeList.length; i++) {
                let n = nodeList[i];
                let widget = JSON.parse(n.widget), beforeData = JSON.parse(widget.beforeData);
                widget.id = n.id;
                widget.w = n.w;
                widget.h = n.h;
                widget.x = n.x;
                widget.y = n.y;
                widget.linkageId = n.linkageId;
                widget.widgetMeta = n.widgetMeta;
                widget.widgetMetaId = n.widgetMetaId || n.widgetMeta.id;
                if (widget.title === undefined) {
                    widget.title = n.title;
                }
                if (widget.name === undefined) {
                    widget.name = n.label;
                }
                beforeData.minW = n.minW;
                beforeData.minH = n.minH;
                widget.beforeData = JSON.stringify(beforeData);
                if (n.code === 'SelectWidget') {
                    widget.query = JSON.stringify(beforeData.filterList);
                } else if (n.code !== 'TextWidget') {
                    // beforeData.filterList = [];
                    widget.beforeData = JSON.stringify(beforeData);
                }
                if (widget.linkageFilter) {
                    widget.linkageFilter = "";
                }
                if (beforeData.drill) {
                    widget.drillFilter = "";
                    widget.widgetDataset.widgetDatasetDimsDrill = [];
                }
                dashboard.widgets.push(widget);
            }
            let result = false;
            settings.loading = true;
            await services.saveOrUpdate(JSON.stringify(dashboard), settings).then(res => {
                if (res.data.status === 1) {
                    result = false;
                } else {
                    result = true;
                    vm.rowData.id = res.data.data;
                    vm.$message.success("保存成功");
                }

            }).catch(err => {
                result = false;
            });
            return result;
        },
        titleChange(val) {
            this.$refs.title.style.minWidth = val.length * 16 + 'px';
            if (val.length > 30) {
                this.$message.info('仪表盘标题限30个字符以内!')
            }
            if (!this.rowData) {
                this.rowData = {};
            }
            this.rowData.name = val;
        },
        reNewRowData(rowData) {
            this.rowData = rowData;
            this.title = this.rowData.name;
        },
        initState(state, filtertxt, groupId) {
            this.groupId = groupId;
            this.filterTxt = filtertxt;
            this.state = state;
            this.stateChange(state);
        },
        initNode(row) {
            if (row) {
                this.rowData = row;
                this.$nextTick(() => {
                    this.$refs.visual.initData(row);
                });
                this.title = this.rowData.name;
                this.titleChange(this.title);
            } else {
                this.title = '';
            }
        },
        stateChange(val) {
            this.$nextTick(() => {
                if (val === 'preview') {
                    this.$refs.visual.fullScreen(true);
                    this.isFullScreen = true;
                    this.$refs.visual.reLoad();
                } else {
                    this.$refs.visual.fullScreen(false);
                    this.isFullScreen = false;
                }
                /*if(this.rowData && this.rowData.data.length > 0){
                    this.rowData.data.forEach(node =>{
                        if(node.widgetMeta.code !== 'SelectWidget'){
                            node.filterList = [];
                            node.widget.query = [];
                        }
                    })
                }*/
            })

        },
        async save() {
            const vm = this;
            let nodeList = this.$refs.visual.nodeList;
            if (vm.showAttr && vm.$refs.tab_attr) {
                await vm.$refs.tab_attr.$refs.attr.$refs.attrPanel.renewAllData(true);
            } else if (vm.showAttr && vm.$refs.sea_attr) {
                await vm.$refs.sea_attr.save(true);
            }
            return await this.saveOrUpdateData(nodeList, this.rowData.groupId, this.title);

        },
        close() {
            const vm = this;
            if (vm.isCase) {
                vm.$emit('closePanel', vm.groupId, vm.filterTxt);
            } else {
                this.msgBox("关闭" , "关闭仪表盘前是否保存?" ,async ()=>{
                    await vm.save();
                    vm.$emit('closePanel', vm.groupId, vm.filterTxt);
                },()=>{
                    vm.$emit('closePanel', vm.groupId, vm.filterTxt);
                } ,()=>{} ,{
                    type : "info",
                    confirmButtonText: '保存',
                    cancelButtonText: '确认关闭',
                    distinguishCancelAndClose :true
                })
            }
        },
        setAttrTitle(node) {
            let title1 = '-控件设置', title2 = '-图表设计';
            if (node.code === 'SelectWidget' || node.code === 'TextWidget') {
                this.attr_title = node.label + title1;
            } else {
                this.attr_title = node.label + title2;
            }
            this.documentCode = this.codeList[node.code];
            this.showChangeIcon = this.chartsType.indexOf(node.code) > -1;
        },
        showAttrPanel(isShow, node) {
            this.loading = true;
            const $this = this;
            this.showChangeCharts = false;
            this.showAttr = isShow;
            this.plug = node;
            this.setAttrTitle(node);
            setTimeout(() => {
                $this.loading = false;
            }, 500);
        },
        closeAttrPanel(evt, node) {
            if (evt || this.plug && this.plug.widgetMeta.id === node.widgetMeta.id) {
                this.showAttr = false;
                // this.$refs.visual.delay();
            }
        },
        setDatasetChildren(data) {
            const vm = this;
            data.forEach(child => {
                if (child.children && child.children.length) {
                    vm.setDatasetChildren(child.children);
                } else {
                    if (!child.isParent) {
                        vm.bkDataset.push(child);
                    }
                }
            })
        },
        async getAllDataset() {
            const vm = this, {visualServices, visualMock} = this;
            let services = vm.getServices(visualServices, visualMock);
            await services.isHaveDataSet().then(res => {
                if (res.data.status === 0) {
                    vm.bkDataset = res.data.data;
                    // await vm.setDatasetChildren(data);
                }
            })
        },
        /**
         * 获取仪表盘数据
         * fjData 替换 分局数据
         * */
        async getRowData({rowid, dataSetId, userCode, passWord, isFirstPage , userId }) {//请求rowdata
            const vm = this, {visualServices, visualMock, settings} = this;
            let services = vm.getServices(visualServices, visualMock);
            if (rowid) {
                await vm.getAllDataset();
                settings.loading = true;
                let previewData;
                if (userCode !== undefined && passWord !== undefined) {
                    if(userCode && passWord){
                        previewData = services.getPreVieWDataByUserCodeAndPassWord(rowid, userCode , passWord);
                    }else {
                        vm.$router.push('/error');
                        settings.loading = false;
                        return;
                    }
                }else if(userId){
                    previewData = services.getPreviewDataByUserId(rowid, userId, true);
                }else if (isFirstPage) {
                    previewData = services.getPreVieWDataByUserId(rowid);
                } else {
                    previewData = services.getDashboard(rowid);
                }
                previewData.then(async res => {
                    if (res.data.status !== 0 || !res.data.data) {
                        settings.loading = false;
                        return;
                    }
                    let d = res.data.data;
                    let fjData = d.filterPolice && d.filterPolice.id ? d.filterPolice : null;
                    let row_datasetId = d.datasetId || dataSetId;
                    let t = {
                        id: d.id,
                        name: d.name,
                        creator: 'ouyang',
                        editorAndDate: d.updateTime,
                        groupId: d.groupId,
                        dataSetId: row_datasetId,
                        isCache: d.isCache,
                        cron: d.cron,
                        data: []
                    };
                    if (row_datasetId) {
                        let hasDataset = vm.bkDataset.find(set => set.id === row_datasetId);
                        if (!hasDataset) {
                            vm.clearMsg = "图表使用的数据集已被删除,相关图表则无数据展示,避免重复提示，请保存。";
                            t.dataSetId = null;
                        }
                    }
                    await vm.pushWidget(t.data, d, fjData);
                    vm.fastCountDatacopy = await vm.requestFastCount(true); //请求快速计算数据
                    vm.makeUpData = await vm.getMakeUpData();
                    await vm.reNewFieldData(t);
                    vm.initNode(t);
                    if (this.clearMsg) {
                        this.$message({message: this.clearMsg, duration: 5000});
                    }
                    setTimeout(() => {
                        settings.loading = false;
                    }, 200);

                }).catch(err => {
                    settings.loading = false;
                })
            } else {
                vm.initNode();
            }
        },
        /**
         * 更新字段数据
         * @param row 方案图表数据
         */
        async reNewFieldData(row) {
            const vm = this, {visualServices, visualMock} = this;
            let services = vm.getServices(visualServices, visualMock);
            for (let list of row.data) {
                if(!list) continue;
                let widget = JSON.parse(list.widget),
                    beforeData = JSON.parse(widget.beforeData), {widgetMeta} = widget,
                    datasetId = beforeData.dataset && beforeData.dataset.id;
                if (datasetId) {
                    let dbType = beforeData.dataset.dbType;
                    await services.getDatasetFields(datasetId).then(res => {
                        if (res.data.status === 0) {
                            vm.allDatasetFields[datasetId] = res.data.data.map(item => {
                                item.dbType = dbType;
                                return item;
                            });
                        }
                    })
                    if (beforeData.xData) await vm.matchAllFields(vm.allDatasetFields[datasetId], beforeData.xData, dbType, widgetMeta);
                    if (beforeData.yData) await vm.matchAllFields(vm.allDatasetFields[datasetId], beforeData.yData, dbType, widgetMeta);
                    if (beforeData.zData) await vm.matchAllFields(vm.allDatasetFields[datasetId], beforeData.zData, dbType, widgetMeta);
                    if (beforeData.kData) await vm.matchAllFields(vm.allDatasetFields[datasetId], beforeData.kData, dbType, widgetMeta);
                    if (beforeData.uData) await vm.matchAllFields(vm.allDatasetFields[datasetId], beforeData.uData, dbType, widgetMeta);
                    widget.beforeData = JSON.stringify(beforeData);
                    list.widget = JSON.stringify(widget);
                }
            }
            vm.reNewSearchField(row.data);
        },
        reNewSearchField(row_data) {
            const vm = this;
            row_data && row_data.forEach(row => {
                if(!row) return;
                let widget = JSON.parse(row.widget),
                    beforeData = JSON.parse(widget.beforeData), {widgetMeta} = widget;
                if (widgetMeta.code === "SelectWidget") {
                    beforeData.filterList && beforeData.filterList.forEach(list => {
                        list.condition && list.condition.forEach(con => {
                            if (con.dataset) {
                                let allFields = vm.allDatasetFields[con.dataset],
                                    field = allFields && allFields.filter(fie => fie.id === con.field.id)[0] || null;
                                if (field) {
                                    con.field = Object.assign(con.field, field);
                                }
                                if (allFields) con.fieldOpts = [...allFields];
                            }
                        })
                    })
                }
            })
        },
        /**
         * 匹配更新字段
         * @param fields 该数据集所有字段
         * @param fData 更新的配置数据字段
         * @param dbtype 库类型
         * @param widgetMeta
         */
        matchAllFields(fields, fData, dbtype, widgetMeta) {
            const vm = this;
            for (const da of fData) {
                let field = fields.filter(fie => fie.id === da.data.id)[0];
                if (field) {
                    let alias = field.fieldAlias ? field.fieldAlias : "";
                    da.data = {
                        ...field,
                        dbtype,
                        alias,
                        label: alias || field.name || field.code,
                    };
                    da.data.dataType = field.dbtype || 'string';
                    vm.reNewDropOpt(da, widgetMeta);
                }
            }
        },
        /**
         * 更新字段下拉功能
         * @param option Axis数据
         * @param widgetMeta
         * */
        async reNewDropOpt(option, widgetMeta) {
            let {indexType, alias, format, exp, mbColumn} = option.data;
            delete option.ratio;
            option.alias = alias;
            option.rank === undefined ? option.rank = {
                label: '排序',
                value: 'none',
                name: '无',
                children: {
                    none: {
                        label: '无'
                    },
                    ascending: {
                        label: '升序'
                    },
                    descending: {
                        label: '降序'
                    },
                }
            } : true;
            option.filter === undefined ? option.filter = {
                label: '过滤',
                func: 'filter'
            } : true;
            if (indexType === "DIMENSION") {
                if (widgetMeta.code === "CombinationWidget" && mbColumn) {
                    option.timeFormat === undefined ? option.timeFormat = {
                        label: "时间格式",
                        value: "none",
                        name: '无',
                    } : true;
                    if (option.timeFormat) option.timeFormat.children = {
                        "none": {label: "无"},
                        "yyyyMMdd": {label: "yyyyMMdd"},
                        "yyyy-MM-dd": {label: "yyyy-MM-dd"},
                        "yyyy.MM.dd": {label: "yyyy年MM月dd日", val: "yyyy年MM月dd日"},
                        "yyyy/MM/dd": {label: "yyyy/MM/dd"},
                    }
                } else {
                    //后期可以删除
                    delete option.timeFormat;
                }
                let dataMakeUpCharts = ["CombinationWidget", "BarChartWidget", "StackBarChartWidget", "TransverseBarChartWidget", "TransverseStackBarChartWidget", "LineChartWidget", "AreaGraphWidget"];
                if (dataMakeUpCharts.indexOf(widgetMeta.code) > -1) {
                    option.dataMakeUp === undefined ? option.dataMakeUp = {
                        label: "数值对齐",
                        value: "none",
                        name: "无"
                    } : true;
                } else {
                    delete option.dataMakeUp;
                }

                if (option.dataMakeUp) option.dataMakeUp.children = this.makeUpData;
            } else if (indexType === "MEASURE") {
                let isExp = this.isExp(exp);
                if (widgetMeta.code !== 'TableChartWidget' && !isExp) {
                    if (option.func === undefined) {
                        option.func = {
                            label: '汇总',
                            value: 'none',
                            name: '无',
                        };
                    }
                    option.func.children = {
                        none: {label: '无'},
                        sum: {label: '总和'},
                        count: {label: '计数'},
                        mean: {label: '平均值'},
                        max: {label: '最大值'},
                        min: {label: '最小值'},
                    }
                }

                let fastCharts = ['BarChartWidget', 'StackBarChartWidget', "TransverseBarChartWidget", "TransverseStackBarChartWidget", 'LineChartWidget', 'AreaGraphWidget', 'CombinationWidget'];
                if (fastCharts.indexOf(widgetMeta.code) > -1) {
                    if (option.fastCount === undefined) {
                        option.fastCount = {
                            label: "快速计算",
                            value: "none",
                            name: "无",
                        };
                    }
                    let {fastCountDatacopy} = this;
                    option.fastCount.children = fastCountDatacopy;
                } else {
                    delete option.fastCount;
                }
            }
        },
        /**
         * fjData ,预览模式下，分局，派出所的code Id
         * */
        async pushWidget(data, d, fjData) {
            const vm = this;
            if (d.widgets.length) {
                let cascadeOptions = [];
                if (fjData) cascadeOptions = await vm.getCascadeOptions(2, fjData.id);
                let widgets = d.widgets.sort(vm.compare('y', true));
                for (let j = 0; j < widgets.length; j++) {
                    let w = widgets[j];
                    data.push(await vm.convert(w, fjData, cascadeOptions));
                }
            }
        },
        /**
         * 设置级联选项 过滤条件
         * */
        getFilterTxt(data, level) {
            let opts = [];
            data.forEach(li => {
                li.filter = {
                    addVal: li.value,
                    value: li.value,
                    type: "suffix_fuzzy",
                    parentType: 'text',
                };
                li.level = level || "";
                opts.push(li);
            });
            return opts;
        },
        /**
         * 获取分局派出所级联数据
         * @param level 级别
         * @param id
         * */
        async getCascadeOptions(level, id) {
            const vm = this, {visualServices, visualMock} = this;
            let services = vm.getServices(visualServices, visualMock);
            let opt = [];
            await services.linkagePoliceStation(level, id).then(async res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    opt = await vm.getFilterTxt(result, level);
                }
            });
            return opt;
        },
        setFilter(field){
            return {
                field,
                value:"",
                addVal : "",
                type: "suffix_fuzzy",
                parentType: 'text',
            }
        },
        /**
         * 根据用户的权限 替换分局，派出所
         * */
        replaceOffice(fjData, chartData, code, cascadeOptions) {
            if (!chartData.filterList) return;
            const vm = this;
            if (code === "SelectWidget") {
                chartData.filterList.forEach(list => {
                    if (list.selectType === "select_cascade" && list.filterOpt) {
                        if(!list.filterOpt[0].filter) list.filterOpt[0].filter = vm.setFilter();
                        list.filterOpt[0].filterVal = fjData.code || "";
                        list.filterOpt[0].filter.value = list.filterOpt[0].filter.addVal = fjData.code || "";
                        list.filterOpt[1].options = cascadeOptions;
                        if (fjData.code){
                            list.filterOpt[0].options = list.filterOpt[0].options.filter(op => op.value === fjData.code);
                            list.filterOpt[0].disabled = true;
                        }
                        if (!list.filterOpt[1].filter) list.filterOpt[1].filter = vm.setFilter();
                        list.filterOpt[1].filter.value = list.filterOpt[1].filter.addVal = fjData.pcsCode || "";
                        list.filterOpt[1].filterVal = fjData.pcsCode || "";
                        if (fjData.pcsCode){
                            list.filterOpt[1].options = list.filterOpt[1].options.filter(op => op.value === fjData.pcsCode);
                            list.filterOpt[1].disabled = true;
                        }
                    }
                })
            } else {
                chartData.filterList.forEach(list => {
                    let fieldCode = list.field.code.toLowerCase();
                    if (fieldCode === "branch_office" || fieldCode === "ssfj_mc_w" || fieldCode === "ssfjmc_w" || fieldCode === "ssfjdm_w" || fieldCode === "ssfj_dm_w" || fieldCode === "fj_dm_w") {
                        if (!list.filter) list.filter = vm.setFilter(list.field);
                        list.fliterVal = fjData.code || "";
                        list.filter.value = list.filter.addVal = fjData.code || "";
                    } else if (fieldCode === "police_station" || fieldCode === "sspcs_mc_w" || fieldCode === "sspcsmc_w" || fieldCode === "sspcsdm_w" || fieldCode === "sspcs_dm_w" || fieldCode === "pcs_dm_w") {
                        if (!list.filter) list.filter = vm.setFilter(list.field);
                        list.fliterVal = fjData.pcsCode || "";
                        list.filter.value = list.filter.addVal = fjData.pcsCode || "";
                    }
                })
            }
        },
        //根据不同的图像做不同的数据
        async convert(w, fjData, cascadeOptions) {
            if (!w.widgetMeta) return;
            let we = {
                width: w.w,
                height: w.h,
                top: w.y,
                left: w.x,
                label: w.name,
                title: w.title,
                id: w.id,
                widgetMeta: w.widgetMeta,
                linkageId: w.linkageId,
                widget: w,
                code: w.widgetMeta.code
            };
            let chartData = JSON.parse(w.beforeData);
            we.minW = chartData.minW;
            we.minH = chartData.minH;
            if (chartData.show !== undefined) {
                we.show = chartData.show;
            } else {
                we.show = true;
            }
            if (fjData) {
                await this.replaceOffice(fjData, chartData, w.widgetMeta.code, cascadeOptions);
            }
            if (w.widgetMeta.code === 'TableChartWidget' && chartData.yData) {
                chartData.yData.forEach(x => {
                    x.func ?
                        x.func = {} : true;
                })
            }
            this.setDefaultPreviewLine(chartData ,w.widgetMeta.code);
            let hasDataset = this.bkDataset.find(set => set.id === chartData.datasetId);
            if (chartData.datasetId && !hasDataset) {
                this.clearMsg = "图表使用的数据集已被删除,相关图表则无数据展示,避免重复提示，请保存。";
                if (w.widgetMeta.code !== 'SelectWidget') {
                    chartData.classifierStatId = we.classifierStatId = "";
                    chartData.dataset = we.dataset = {};
                    chartData.datasetId = we.datasetId = "";
                    chartData.filterData = we.filterData = [];
                    chartData.xAxis = we.xAxis = [];
                    chartData.yAxis = we.yAxis = [];
                    chartData.xData = we.xData = [];
                    chartData.yData = we.yData = [];
                }
            }
            we.widget.beforeData = JSON.stringify(chartData);
            we.widget = JSON.stringify(we.widget);
            return we;
        },
        /**
         * 设置表单默认的预览行数
         * @param chartData
         * @param code
         */
        setDefaultPreviewLine(chartData , code){
            if(code !== "FormWidget") return ;
            chartData.previewLine === undefined ? chartData.previewLine = "100" : true;
        },
        getRouterData(id, filterTxt, groupId, dataSetId) {
            let path = this.$route.fullPath;
            if (path.indexOf('/datacenter/visualview') > -1) { //查看状态
                this.viewing = true;
                this.initState('preview', filterTxt, groupId);
                let {rowid, userCode , passWord , userId, userToken } = this.$route.query;
                if(userToken && rowid) {
                    return this.loginByToken(userToken,rowid);
                }
                //通过id请求rowdata
                if (rowid) {
                    let isFirstPage = false;
                    if (path.indexOf('key=firstPage') > -1) isFirstPage = true;
                    this.getRowData({rowid, dataSetId:'', userCode, passWord,userId, isFirstPage});
                } else {
                    this.$router.push("/");
                }
            } else { //编辑状态
                this.viewing = false;
                this.initState('edit', filterTxt, groupId);
                this.getRowData({rowid:id, dataSetId});
            }
        },
        loginByToken(userToken, rowid){
            const redirectUrl = `http://${window.location.host}${this.$route.path}?rowid=${rowid}`;
            this.$router.push({
                path: "/",
                query: {
                    userToken,
                    redirectUrl
                }
            })
        }
    },
    created() {
        this.getRouterData();
    },
    destroyed() {
        clearInterval(this.timer);
        this.timer = null;
    }
}
</script>

<style scoped lang="less">


.visualizationEdit {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    background: #f2f2f2;
    z-index: 50;
}

.ce-panel_title-bg {
    overflow: hidden;
    background: #fafafa;
    border-bottom: 1px solid #ddd;
}

.ce-panel_title {
    line-height: 40px;
    height: 40px;
    float: left;
    color: @font-color;
    font-size: 16px;
    padding: 0 16px;
}

.ce-left-panel {
    margin: 8px;
    height: calc(100% - 60px);
    position: relative;
    border: 1px solid #ccc;
    transition: 500ms;
    overflow: hidden;
}

.ce-marginL {
    margin-right: 546px;
}

.ce-is-full {
    margin-right: 8px;
}

.ce-right-attr {
    width: 530px;
    float: right;
    background: #fff;
    height: calc(100% - 60px);
    border: 1px solid #ccc;
    margin: 8px 8px 0 0;
    transition: 500ms;
}

.ce-attr_hide {
    width: 0;
}

.ce-attr_title {
    line-height: 36px;
    height: 36px;
    color: #000;
    background: #e9e9eb;
    padding: 0 10px;
    font-size: 14px;
    box-sizing: border-box;
    border-bottom: 1px solid #ccc;
}

.ce-buttons_group {
    float: right;
    padding: 5px 10px 0 0;
}

.ce-radio {
    vertical-align: middle;
    margin-right: 10px;
}

.ce-btn {
    vertical-align: middle;
    margin-left: 10px;
    line-height: 1;
}

.ce-attr_close {
    float: right;
    cursor: pointer;
    margin: 10px 0 0 0;
    font-size: 16px;
}

.ce-attr_close:hover {
    color: #fff;
    background: #53a8ff;
}

.ce-visual_title {
    display: inline-block;
}

.ce-icon-rotate {
    margin: 10px 10px 0 0;
    transform: rotate(90deg);
}
</style>
<style>
.ce-visual_title > .el-input > input {
    border: none;
    outline: none;
    background: none;
    border-radius: 0;
    border-bottom: 1px solid #ccc;
    color: #289bf1;
    font-size: 14px;
    padding: 0
}
</style>
