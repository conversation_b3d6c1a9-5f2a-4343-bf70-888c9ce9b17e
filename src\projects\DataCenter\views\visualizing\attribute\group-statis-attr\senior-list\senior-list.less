/**
 * 分组统计表格高级配置样式
 */
.groupStatisSeniorList {
    .el-form {
        .el-form-item {
            margin-bottom: 20px;
            
            .el-form-item__label {
                font-size: 12px;
                color: #606266;
                font-weight: 500;
            }
            
            .el-form-item__content {
                .config-tip {
                    font-size: 11px;
                    color: #909399;
                    margin-top: 5px;
                    font-style: italic;
                    display: block;
                }
            }
        }
    }
    
    .el-input-number {
        width: 120px;
        
        .el-input__inner {
            font-size: 12px;
            height: 28px;
            line-height: 28px;
            text-align: center;
        }
        
        .el-input-number__decrease,
        .el-input-number__increase {
            height: 14px;
            line-height: 14px;
        }
    }
    
    .el-select {
        .el-input__inner {
            font-size: 12px;
            height: 28px;
            line-height: 28px;
        }
    }
    
    .el-input {
        .el-input__inner {
            font-size: 12px;
            height: 28px;
            line-height: 28px;
        }
    }
    
    .el-checkbox {
        .el-checkbox__label {
            font-size: 12px;
            color: #606266;
            padding-left: 8px;
        }
        
        &.is-checked {
            .el-checkbox__label {
                color: #409eff;
                font-weight: 500;
            }
        }
    }
    
    // 刷新配置特殊样式
    .input-with-select {
        .el-input-group__append {
            .ce-select_in {
                width: 60px;
                
                .el-input__inner {
                    border: none;
                    background-color: transparent;
                }
            }
        }
    }
    
    .ce-tip {
        font-size: 11px;
        color: #909399;
        margin-left: 10px;
    }
}
