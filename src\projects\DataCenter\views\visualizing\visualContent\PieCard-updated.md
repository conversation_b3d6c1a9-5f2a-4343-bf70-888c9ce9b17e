# PieCard 组件功能更新说明

## 修改内容

### 1. 数据格式适配
根据提供的实际数据格式，修正了 `generateTableData()` 方法：

```javascript
// 原始数据格式
{
  "columns": ["民族名称", "年龄段"],
  "rows": [
    { "民族名称": "回族", "年龄段": 1 },
    { "民族名称": "满族", "年龄段": 2 },
    { "民族名称": "汉族", "年龄段": 92 }
  ],
  "dimsCodes": ["民族名称"],
  "formatMapping": {}
}
```

### 2. 关键修改点

#### 数据访问方式
- **修改前**: `row.name`, `row.value`
- **修改后**: `row[dimensionColumn]`, `row[measureColumn]`

#### 列标题处理
- 动态获取维度列和度量列的名称
- 支持字段别名显示
- 正确映射到表格列标题

#### 数据计算
- 使用 `columns[0]` 作为维度字段（分类）
- 使用 `columns[1]` 作为度量字段（数值）
- 正确计算总值和百分比

### 3. 核心代码修改

```javascript
generateTableData() {
    // 获取列名
    let dimensionColumn = this.chartData.columns[0]; // "民族名称"
    let measureColumn = this.chartData.columns[1];   // "年龄段"
    
    // 获取字段别名
    let measureLabel = measureField ? (measureField.alias || measureField.label) : measureColumn;
    let dimensionLabel = dimensionField ? (dimensionField.alias || dimensionField.label) : dimensionColumn;
    
    // 计算总值
    this.chartData.rows.forEach(row => {
        let value = parseFloat(row[measureColumn] || 0);
        totalValue += value;
    });
    
    // 生成表格数据
    this.tableData = this.chartData.rows.map((row, index) => {
        let rawValue = parseFloat(row[measureColumn] || 0);
        return {
            name: row[dimensionColumn] || '', // 使用维度列的值
            value: formattedValue,
            originalValue: rawValue,
            percentage: parseFloat(percentage),
            color: this.getItemColor(index),
            colorIndicator: ''
        };
    });
}
```

### 4. 功能特性

#### 表格显示
- ✅ 颜色指示器列
- ✅ 分类名称列（支持别名）
- ✅ 数值列（支持格式化和别名）
- ✅ 百分比列（带进度条可视化）

#### 数据处理
- ✅ 自动计算百分比
- ✅ 按数值降序排序
- ✅ 支持数字格式化
- ✅ 动态列标题

#### 交互功能
- ✅ 饼图/表格切换
- ✅ 响应式设计
- ✅ 数据同步更新

### 5. 测试数据

创建了 `PieCard-demo.vue` 演示页面，使用实际数据格式进行测试：

```javascript
testData: {
    columns: ["民族名称", "年龄段"],
    rows: [
        { "民族名称": "回族", "年龄段": 1 },
        { "民族名称": "满族", "年龄段": 2 },
        { "民族名称": "汉族", "年龄段": 92 },
        { "民族名称": "蒙古族", "年龄段": 5 },
        { "民族名称": "藏族", "年龄段": 3 }
    ],
    dimsCodes: ["民族名称"],
    formatMapping: {}
}
```

### 6. 兼容性保证

- ✅ 完全兼容原有饼图功能
- ✅ 不影响现有配置
- ✅ 支持所有饼图类型
- ✅ 保持原有样式和交互

## 使用说明

1. 点击组件右上角的切换按钮
2. 选择"表格"模式查看分组统计表格
3. 表格会自动根据 chartData 生成
4. 支持实时数据更新和同步
