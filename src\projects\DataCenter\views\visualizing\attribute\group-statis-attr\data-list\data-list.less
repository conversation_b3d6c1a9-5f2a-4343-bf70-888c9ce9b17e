/**
 * 分组统计表格数据配置样式
 */
.groupStatisDataList {
    .group-config-section {
        padding: 15px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        margin-bottom: 15px;
        background-color: #fafafa;
        
        .section-title {
            font-size: 13px;
            color: #303133;
            margin-bottom: 12px;
            font-weight: 600;
            border-bottom: 1px solid #e4e7ed;
            padding-bottom: 8px;
        }
        
        .config-item {
            margin-bottom: 15px;
            
            &:last-child {
                margin-bottom: 0;
            }
            
            .item-label {
                font-size: 12px;
                color: #606266;
                margin-bottom: 8px;
                font-weight: 500;
                display: block;
            }
        }
    }
    
    .group-field-container {
        .el-select {
            .el-tag {
                background-color: #ecf5ff;
                border-color: #b3d8ff;
                color: #409eff;
                
                .el-tag__close {
                    color: #409eff;
                    
                    &:hover {
                        background-color: #409eff;
                        color: #fff;
                    }
                }
            }
        }
    }
    
    .statistics-config {
        .el-checkbox-group {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
            
            .el-checkbox {
                margin-right: 0;
                margin-bottom: 0;
                
                .el-checkbox__label {
                    font-size: 12px;
                    color: #606266;
                    padding-left: 8px;
                }
                
                &.is-checked {
                    .el-checkbox__label {
                        color: #409eff;
                        font-weight: 500;
                    }
                }
            }
        }
    }
    
    .el-select {
        .el-input__inner {
            font-size: 12px;
            height: 28px;
            line-height: 28px;
        }
        
        .el-input__suffix {
            .el-input__suffix-inner {
                line-height: 28px;
            }
        }
    }
    
    .el-row {
        .el-col {
            .el-select {
                width: 100%;
            }
        }
    }
}
