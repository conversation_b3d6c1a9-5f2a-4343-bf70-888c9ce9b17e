<template>
    <div class="chartAttr newAttr groupStatisDataList">
        <!-- 基础图表配置 -->
        <div class="ce-attr_form ce-attr_form-axis">
            <chart-base
                :axisList="axisList"
                :showDrill="showDrill"
                :filterSelects="filterSelects"
                :chartData="chartData"
                @allowDrop="allowDrop"
                @dropEnd="dropEnd"
                @changeVal="changeVal"
                @duplicateFun="duplicateFun"
                @nodeFilter="nodeFilter"
                @showDrilling="showDrilling"
                @deleteNode="deleteNode"
                @changeFastCount="changeFastCount"
                @deleteDrill="deleteDrill"
                @drillDropEnd="drillDropEnd"
                @addDrilling="addDrilling"
                @deleteDrillNode="deleteDrillNode"
                @deleteDrillDime="deleteDrillDime"
                @deleteFilter="deleteFilter"
                @inputData="inputData"
                @addFilter="addFilter"
                @typeCheck="typeCheck"
                @dropFilter="dropFilter"
                @filterData="filterData"
                @updateValue="updateValue"
            ></chart-base>
        </div>
        <!-- 数据节点树 -->
        <div class="ce-data_tree ce-data_tree-axis">
            <DataNodeTree ref="nodeTree" :dir_dataSetId="dir_dataSetId" @resetList="resetList" @inputData="inputData"/>
        </div>

        <!-- 过滤器配置 -->
        <FilterDialog ref="filter_dialog" @filterData="setFilter"/>
    </div>
</template>

<script src="./data-list.js"></script>
<style scoped lang="less" src="../../css/attr.less"></style>
<style scoped lang="less" src="./data-list.less"></style>
