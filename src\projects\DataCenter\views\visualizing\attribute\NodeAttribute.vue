<template>
    <div class="nodeAttribute">
        <HistogramAttr ref="attrPanel" :nodeList="nodeList" :dir_dataSetId="dir_dataSetId" :node="editNode"  v-if="plug.code === 'BarChartWidget' || plug.code === 'StackBarChartWidget'|| plug.code === 'TransverseBarChartWidget'|| plug.code === 'TransverseStackBarChartWidget'"/><!--柱状图 堆叠柱图判断-->
        <LineAttr ref="attrPanel" :nodeList="nodeList" :dir_dataSetId="dir_dataSetId" :node="editNode" v-if="plug.code === 'LineChartWidget' || plug.code === 'AreaGraphWidget'"/><!--折线图 面积图-->
        <PieAttr ref="attrPanel" :nodeList="nodeList" :dir_dataSetId="dir_dataSetId" :node="editNode"  v-if="plug.code === 'PieChartWidget' || plug.code === 'RingPieChartWidget'"/><!--饼图 环图-->
        <TableAttr ref="attrPanel" :nodeList="nodeList" :dir_dataSetId="dir_dataSetId" :node="editNode"  v-if="plug.code === 'TableChartWidget'"/><!--表格-->
        <MapAttr ref="attrPanel" :nodeList="nodeList" :dir_dataSetId="dir_dataSetId" :node="editNode"  v-if="plug.code === 'MapChartWidget' ||  plug.code === 'ColourMap'"/><!--地图-->
        <IndicatorAttr ref="attrPanel" :nodeList="nodeList" :dir_dataSetId="dir_dataSetId" :node="editNode"  v-if="plug.code === 'IndicatorCardWidget'"/> <!--指标卡-->
        <TabAttr ref="attrPanel" :nodeList="nodeList" :dir_dataSetId="dir_dataSetId" :node="editNode"  v-if="plug.code === 'TabWidget'" /><!--tab页签-->
        <HeatMapAttr ref="attrPanel" :nodeList="nodeList" :dir_dataSetId="dir_dataSetId" :node="editNode"  v-if="plug.code === 'HeatMap'" /><!--热力图-->
        <WordCloudAttr ref="attrPanel" :nodeList="nodeList" :dir_dataSetId="dir_dataSetId" :node="editNode"  v-if="plug.code === 'WordCloudWidget'" /><!--词云图-->
        <pgisAttr ref="attrPanel" :nodeList="nodeList" :dir_dataSetId="dir_dataSetId" :node="editNode"  v-if="plug.code === 'PGISWidget'" /><!--pgis图-->
        <CombinationAttr ref="attrPanel" :nodeList="nodeList" :dir_dataSetId="dir_dataSetId" :node="editNode"  v-if="plug.code === 'CombinationWidget'" /><!--组合图-->
        <radarAttr ref="attrPanel" :nodeList="nodeList" :dir_dataSetId="dir_dataSetId" :node="editNode"  v-if="plug.code === 'RadarWidget'" /><!--雷达图-->
        <bubbleMapAttr ref="attrPanel" :nodeList="nodeList" :dir_dataSetId="dir_dataSetId" :node="editNode"  v-if="plug.code === 'BubbleMap'" /><!--气泡地图-->
        <graphAttr ref="attrPanel" :nodeList="nodeList" :dir_dataSetId="dir_dataSetId" :node="editNode"  v-if="plug.code === 'RelationshipWidget'" /><!--关系图-->
        <businessRelationAttr ref="attrPanel" :nodeList="nodeList" :dir_dataSetId="dir_dataSetId" :node="editNode"  v-if="plug.code === 'BusinessRelationshipWidget'" /><!--业务关系图-->
        <formAttr ref="attrPanel" :nodeList="nodeList" :dir_dataSetId="dir_dataSetId" :node="editNode"  v-if="plug.code === 'FormWidget'" /><!--表单图-->
        <groupStatisAttr ref="attrPanel" :nodeList="nodeList" :dir_dataSetId="dir_dataSetId" :node="editNode"  v-if="plug.code === 'GroupStatisWidget'" /><!--分组统计表格-->
    </div>
</template>

<script>
    import {globalBus} from "@/api/globalBus";
    import HistogramAttr from './histogram-attr/index.vue'
    import LineAttr from './line-attr/index'
    import PieAttr from './pie-attr/index'
    import TableAttr from './table-attr/index'
    import MapAttr from './map-attr/index'
    import IndicatorAttr from "./indicator-attr/index"
    import TabAttr from "./tab-attr/index"
    import HeatMapAttr from "./heatMap-attr/index"
    import WordCloudAttr from "./wordClound-attr/index"
    import CombinationAttr from "./combination-attr/index"
    import pgisAttr from "./pgis-attr/index"
    import bubbleMapAttr from "./bubble-map-attr/index"
    import radarAttr from "./radar-attr/index"
    import graphAttr from "./graph-attr/index"
    import formAttr from "./form-attr/index"
    import businessRelationAttr from "./businessRelation-attr/index"
    import groupStatisAttr from "./group-statis-attr/index"
    export default {
        name: "NodeAttribute",
        props: {
            plug: Object ,
            dir_dataSetId : String ,
            nodeList : Array
        },
        components: {
            HistogramAttr ,
            LineAttr ,
            PieAttr ,
            TableAttr ,
            MapAttr,
            IndicatorAttr,
            TabAttr,
            HeatMapAttr,
            WordCloudAttr,
            pgisAttr,
            CombinationAttr,
            bubbleMapAttr,
            radarAttr,
            graphAttr,
            formAttr,
            businessRelationAttr,
            groupStatisAttr,
        },
        data() {
            return {
                editNode : null
            }
        },
        methods: {
            setVisualPanel() {
                globalBus.$on('editVisualPanel', this.getVisualId );
            },
            getVisualId(node) {
                this.editNode = node;
                this.$nextTick(()=>{
                    this.$refs.attrPanel.initValue();
                })
            }
        },
        created() {
            this.setVisualPanel();
        },
        destroyed() {
            globalBus.$off('editVisualPanel', this.getVisualId);
        }

    }
</script>

<style scoped>
    .nodeAttribute {
        height: calc(100vh - 95px);
        overflow: auto;
        /*padding: 10px;*/
    }
</style>