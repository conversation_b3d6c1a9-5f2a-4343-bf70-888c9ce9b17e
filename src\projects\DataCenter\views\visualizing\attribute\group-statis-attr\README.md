# 分组统计表格组件

## 概述

分组统计表格组件是按照可视化系统设计思路，参考饼图模式实现的表格分组统计功能。该组件完全遵循系统的架构模式和设计规范。

## 组件架构

### 1. 分层架构
```
分组统计组件
├── 属性配置层 (group-statis-attr/)
│   ├── 数据配置 (data-list/)
│   ├── 样式配置 (style-list/)
│   └── 高级配置 (senior-list/)
└── 可视化展示层 (GroupStatisCard.vue)
```

### 2. 设计模式
- **MVC模式**: 数据-视图-控制分离
- **组件化**: 模块化开发，职责单一
- **混入模式**: 复用通用功能
- **事件驱动**: 通过globalBus进行组件通信

## 核心功能

### 数据配置 (data-list)
- **分组字段**: 支持多个维度字段进行分组
- **统计类型**: 计数、求和、平均值、最大值、最小值
- **统计字段**: 可选择需要统计的数值字段
- **排序设置**: 支持按任意字段进行排序

### 样式配置 (style-list)
- **标题样式**: 字体、颜色、对齐方式等
- **表格样式**: 尺寸、边框、斑马纹、表头样式
- **列样式**: 列宽模式、文字对齐、分页设置
- **字段设置**: 字段别名配置

### 高级配置 (senior-list)
- **自动刷新**: 定时刷新数据
- **数据限制**: 最大行数、空值处理、零值处理
- **性能优化**: 虚拟滚动、懒加载
- **导出配置**: 支持多种格式导出
- **联动配置**: 图表联动设置

## 技术实现

### 1. 组件注册
```javascript
// 属性配置组件注册 (NodeAttribute.vue)
<groupStatisAttr v-if="plug.code === 'GroupStatisWidget'" />

// 可视化组件注册 (charts-index.js)
export { default as GroupStatisCard } from '../GroupStatisCard';

// 画布组件使用 (VisualPanel.vue)
<GroupStatisCard v-if="node.code === 'GroupStatisWidget'" />
```

### 2. 数据流
```
用户配置 → data-list → nodeDataRenew事件 → globalBus → GroupStatisCard → 表格渲染
```

### 3. 混入使用
- **attrCommon**: 属性配置通用功能
- **attrMixins**: 数据配置混入
- **attrStyle**: 样式配置混入
- **attrSenior**: 高级配置混入
- **chartMxins**: 图表渲染混入

## 使用方法

### 1. 在可视化编辑器中使用
1. 从组件库拖拽"分组统计表格"组件到画布
2. 组件代码为: `GroupStatisWidget`
3. 在右侧属性面板进行配置

### 2. 配置步骤
1. **数据配置**: 拖拽维度字段到分组字段，拖拽度量字段到统计字段
2. **样式配置**: 设置表格外观、列样式等
3. **高级配置**: 设置性能优化、导出等高级选项

### 3. 编程方式使用
```vue
<template>
    <div>
        <!-- 属性配置 -->
        <group-statis-attr
            ref="groupStatisAttr"
            :node="node"
            :dir_dataSetId="dataSetId"
            @nodeDataRenew="handleConfigChange">
        </group-statis-attr>
        
        <!-- 可视化展示 -->
        <GroupStatisCard
            ref="groupStatisCard"
            :node="node">
        </GroupStatisCard>
    </div>
</template>
```

## 配置示例

### 基础配置
```javascript
const config = {
    // 分组字段
    groupFields: ['region', 'category'],
    
    // 统计类型
    statisticsTypes: ['count', 'sum', 'avg'],
    
    // 统计字段
    statisticsFields: ['amount', 'quantity'],
    
    // 表格样式
    tableStyle: {
        tableSize: 'small',
        showBorder: true,
        showStripe: true,
        headerBgColor: '#f5f7fa',
        headerTextColor: '#303133'
    },
    
    // 列样式
    columnStyle: {
        columnWidthMode: 'auto',
        textAlign: 'left',
        showPagination: true,
        pageSize: 20
    }
};
```

## 系统集成

### 1. 组件代码
- **组件标识**: `GroupStatisWidget`
- **属性组件**: `groupStatisAttr`
- **可视化组件**: `GroupStatisCard`

### 2. 事件通信
- **配置更新**: `nodeDataRenew`
- **联动触发**: `setLinkage`
- **数据刷新**: `renewAllData`

### 3. 数据接口
- **getAllSettings()**: 获取完整配置
- **initValue()**: 初始化组件
- **preview()**: 预览更新

## 扩展说明

该组件完全按照系统架构设计，具有良好的扩展性：

1. **新增统计类型**: 在data-list中扩展statisticsTypes
2. **新增样式配置**: 在style-list中添加新的settingForm
3. **新增高级功能**: 在senior-list中扩展配置项
4. **自定义渲染**: 在GroupStatisCard中修改表格渲染逻辑

## 注意事项

1. 确保组件代码为 `GroupStatisWidget`
2. 分组字段必须是维度类型
3. 统计字段必须是度量类型
4. 大数据量时建议启用虚拟滚动
5. 导出功能需要浏览器支持文件下载

## 技术特点

- ✅ 完全遵循系统架构
- ✅ 参考饼图模式实现
- ✅ 支持完整的配置体系
- ✅ 集成事件通信机制
- ✅ 使用系统混入功能
- ✅ 支持联动和钻取
- ✅ 响应式设计
- ✅ 性能优化支持
