<template>
    <div class="visualPlug selectn">
        <auto-container ref="container">
            <template slot="contain">
                <PlugInGroup v-for="(plug , index) in plugIn" :key="index" :ref="'slide'+index" :plugInData="plug"/>
            </template>
        </auto-container>
    </div>
</template>

<script>
    import PlugInGroup from '../../modeling/plugin/PlugInGroup'
    import ModelPlugIn from '../../modeling/plugin/ModelPlugIn'
    import AutoContainer from "@/components/common/auto-container/AutoContainer";
    import {commonMixins} from "@/api/commonMethods/common-mixins";
    import {servicesMixins} from "../service-mixins/service-mixins";

    export default {
        name: "VisualPlug",
        mixins : [commonMixins , servicesMixins],
        components: {
            PlugInGroup,
            AutoContainer
        },
        props : {
            showAttr : Boolean,
            isView : Boolean
        },
        extends: ModelPlugIn,
        watch : {
            showAttr(){
                this.$refs.container.resize();
            }
        },
        data() {
            return {
                plugIn: [
                    {
                        plugList: [
                            {
                                icon: '',
                                label: '查询',
                                code : 'SelectWidget'
                            }, {
                                icon: '',
                                label: '文本',
                                code : 'TextWidget'
                            },
                        ],
                        groupName: ''
                    },
                    {
                        plugList: [
                            {
                                icon: '',
                                label: '柱状图',
                                code : 'BarChartWidget'

                            }, {
                                icon: '',
                                label: '折线图',
                                code : 'LineChartWidget'
                            }, {
                                icon: '',
                                label: '饼图',
                                code : 'PieChartWidget'
                            }, {
                                icon: '',
                                label: '表格',
                                code : ''
                            }, {
                                icon: '',
                                label: '地图',
                                code : 'MapChartWidget'
                            },
                        ],
                        groupName: ''
                    }
                ]
            }
        },
        created() {
            this.initData();
        },
        methods: {
            async initData() {
                this.plugIn = [];
                if(this.isView)return;
                await this.setGroup();
                this.$refs.container.resize();
            },

            async setGroup(){
                const vm = this , {visualServices , visualMock} = this;
                let services = vm.getServices(visualServices , visualMock);
                await services.getNoChart().then(res => {
                    if(res.data.status === 0){
                        let data = res.data.data;
                        vm.plugIn.push(vm.getOtherGroup(data));
                    }
                });
                await services.getChartGroup().then(res => {
                    if(res.data.status === 0){
                        let data = res.data.data;
                        vm.plugIn.push(vm.getChartGroup(data));
                    }
                });
                await vm.initPlug();
                vm.$emit("getPlugins" , vm.plugIn);
            },
            getChartGroup: function (chart) {
                let group = {plugList: [], groupName: ''};
                for (let i = 0; i < chart.length; i++) {
                    let d=chart[i];
                    if(d.widgetMetas.length===1){
                        let o = {
                            icon: '',
                            label: d.widgetMetas[0].name,
                            code:d.widgetMetas[0].code,
                            widgetMeta:d.widgetMetas[0]
                        };
                        group.plugList.push(o);
                    }else {
                        let o = {
                            icon: '',
                            label:d.name,
                            children:[]
                        };
                        for(let j=0;j<d.widgetMetas.length;j++){
                            let w=d.widgetMetas[j];
                            let c={
                                icon: '',
                                label: w.name,
                                code:w.code,
                                widgetMeta:w
                            };
                            o.children.push(c);
                        }
                        group.plugList.push(o);
                    }

                }
                return group;
            },
            getOtherGroup: function (data) {
                let group = {plugList: [], groupName: ''};
                for (let i = 0; i < data.length; i++) {
                    let d = data[i];
                    group.plugList.push({icon: '', label: d.name,code:d.code,widgetMeta:d});
                }
                return group;
            }

        }
    }
</script>

<style scoped>
    .visualPlug {
        position: relative;
        border-bottom: 1px solid #ccc;
        background: #fff;
        z-index: 999;
        height: 75px;
    }
</style>
