# PieCard 嵌套饼图表格功能

## 功能概述

PieCard 组件现在完全支持嵌套饼图（RingPieChartWidget）的表格展示，能够将复杂的两层维度数据以层级表格的形式呈现。

## 数据格式支持

### 普通饼图数据格式
```json
{
  "columns": ["民族名称", "年龄段"],
  "rows": [
    { "民族名称": "回族", "年龄段": 1 },
    { "民族名称": "满族", "年龄段": 2 },
    { "民族名称": "汉族", "年龄段": 92 }
  ],
  "dimsCodes": ["民族名称"],
  "formatMapping": {}
}
```

### 嵌套饼图数据格式
```json
{
  "columns": {
    "dims": ["zgxlmc", "mzmc"],
    "measures": "xbmc"
  },
  "rows": [
    {
      "name": "汉族",
      "value": 92,
      "child": {
        "中等专科结业": "1",
        "中等职业教育": "4",
        "初中毕业": "31",
        "初级中学教育": "13"
      }
    }
  ],
  "dimsCodes": ["民族名称", "最高学历名称"]
}
```

## 核心功能实现

### 1. 智能数据格式识别
```javascript
// 判断是否为嵌套饼图
let isNestedPie = vm.node.code === 'RingPieChartWidget' && 
                  nodeData.yData.length > 1 && 
                  !vm.showDrill;

if (isNestedPie) {
    this.generateNestedPieTableData(nodeData);
} else {
    this.generateNormalPieTableData(nodeData);
}
```

### 2. 嵌套表格数据生成
- **外层汇总行**: 显示每个主分类的总计数据
- **内层明细行**: 显示每个主分类下的子分类详情
- **层级结构**: 使用缩进和树形符号表示层级关系

### 3. 表格列配置动态调整
```javascript
// 嵌套饼图的表格列配置
this.tableColumns = [
    { prop: 'colorIndicator', label: '', width: 50 },
    { prop: 'outerCategory', label: outerLabel, minWidth: 100 },
    { prop: 'innerCategory', label: innerLabel, minWidth: 120 },
    { prop: 'value', label: measureDisplayLabel, minWidth: 80 },
    { prop: 'percentage', label: '占比', minWidth: 120 }
];
```

## 表格展示特性

### 层级结构展示
- **外层汇总行**: `【汉族小计】` - 显示主分类总计
- **内层明细行**: `　├ 初中毕业` - 显示子分类详情

### 样式差异化
- **外层行**: 背景色 `#f8f9fa`，字体加粗，上边框加粗
- **内层行**: 背景色 `#fdfdfd`，左缩进，字体较小，颜色较淡

### 颜色指示器
- 只在外层汇总行显示颜色点
- 内层明细行不显示颜色指示器
- 同一外层分类使用相同颜色

### 百分比计算
- 基于总体数据计算每行的占比
- 外层行显示该分类占总体的百分比
- 内层行显示该子分类占总体的百分比

## 使用示例

### 基本用法
```vue
<PieCard 
    :node="nestedPieNode" 
    :id="'nested-pie-chart'"
    :isView="false"
/>
```

### 节点配置
```javascript
nestedPieNode: {
    id: 'nested-pie-chart',
    code: 'RingPieChartWidget', // 关键：必须是 RingPieChartWidget
    widget: JSON.stringify({
        beforeData: JSON.stringify({
            xData: [{ /* 度量字段配置 */ }],
            yData: [
                { /* 外层维度配置 */ },
                { /* 内层维度配置 */ }
            ],
            style: { /* 样式配置 */ }
        })
    })
}
```

## 技术实现细节

### 数据处理流程
1. **格式识别**: 检查 `node.code` 和 `yData.length`
2. **字段映射**: 从 `dimsCodes` 获取维度字段名
3. **数据展开**: 将嵌套结构展开为平铺表格数据
4. **样式标记**: 为不同类型的行添加标识
5. **排序处理**: 保持原有的数据顺序

### 关键方法
- `generateNestedPieTableData()`: 生成嵌套表格数据
- `getRowClassName()`: 设置行样式类名
- `formatValue()`: 数值格式化处理

### 样式系统
- 使用 CSS 类名区分不同类型的行
- 支持响应式设计
- 保持与普通表格的一致性

## 兼容性保证

- ✅ 完全兼容普通饼图功能
- ✅ 不影响现有的配置和样式
- ✅ 支持所有数字格式化选项
- ✅ 保持原有的交互功能
- ✅ 支持图表/表格切换

## 测试验证

### 测试文件
- `PieCard-nested-test.vue`: 嵌套饼图功能测试页面
- 包含普通饼图和嵌套饼图的对比测试
- 展示完整的数据格式和功能特性

### 测试场景
1. 普通饼图表格展示
2. 嵌套饼图表格展示
3. 层级结构正确性
4. 样式差异化效果
5. 数据计算准确性

现在 PieCard 组件能够智能识别数据格式，为普通饼图和嵌套饼图提供最适合的表格展示方式。
