<template>
    <div class="scroll-test">
        <h2>PieCard 表格滚动条功能测试</h2>
        
        <div class="test-grid">
            <!-- 少量数据测试 -->
            <div class="test-item">
                <h3>少量数据（无滚动条）</h3>
                <div class="chart-container">
                    <PieCard 
                        :node="smallDataNode" 
                        :id="'small-data-pie'"
                        :isView="false"
                    />
                </div>
            </div>
            
            <!-- 大量数据测试 -->
            <div class="test-item">
                <h3>大量数据（有滚动条）</h3>
                <div class="chart-container">
                    <PieCard 
                        :node="largeDataNode" 
                        :id="'large-data-pie'"
                        :isView="false"
                    />
                </div>
            </div>
            
            <!-- 嵌套饼图大量数据测试 -->
            <div class="test-item">
                <h3>嵌套饼图大量数据（有滚动条）</h3>
                <div class="chart-container">
                    <PieCard 
                        :node="nestedLargeDataNode" 
                        :id="'nested-large-data-pie'"
                        :isView="false"
                    />
                </div>
            </div>
            
            <!-- 固定高度容器测试 -->
            <div class="test-item">
                <h3>固定高度容器（200px）</h3>
                <div class="chart-container small-container">
                    <PieCard 
                        :node="fixedHeightNode" 
                        :id="'fixed-height-pie'"
                        :isView="false"
                    />
                </div>
            </div>
        </div>
        
        <div class="feature-description">
            <h3>滚动条功能特性：</h3>
            <ul>
                <li>✅ 自动检测表格内容是否超出容器高度</li>
                <li>✅ 超出时自动显示滚动条</li>
                <li>✅ 自定义滚动条样式，美观且易用</li>
                <li>✅ 表头固定，滚动时保持可见</li>
                <li>✅ 响应式设计，移动端适配</li>
                <li>✅ 滚动提示效果，底部渐变遮罩</li>
                <li>✅ 支持普通饼图和嵌套饼图</li>
            </ul>
        </div>
    </div>
</template>

<script>
import PieCard from './PieCard.vue'

export default {
    name: 'PieCardScrollTest',
    components: {
        PieCard
    },
    data() {
        return {
            // 少量数据
            smallData: {
                columns: ["类别", "数值"],
                rows: [
                    { "类别": "类别A", "数值": 35 },
                    { "类别": "类别B", "数值": 25 },
                    { "类别": "类别C", "数值": 20 }
                ],
                dimsCodes: ["类别"],
                formatMapping: {}
            },
            
            // 大量数据
            largeData: {
                columns: ["产品", "销量"],
                rows: Array.from({length: 25}, (_, i) => ({
                    "产品": `产品${String.fromCharCode(65 + i)}`,
                    "销量": Math.floor(Math.random() * 100) + 10
                })),
                dimsCodes: ["产品"],
                formatMapping: {}
            },
            
            // 嵌套饼图大量数据
            nestedLargeData: {
                columns: {
                    dims: ["category", "subcategory"],
                    measures: "value"
                },
                rows: [
                    {
                        name: "电子产品",
                        value: 150,
                        child: Object.fromEntries(
                            Array.from({length: 15}, (_, i) => [
                                `电子产品子类${i + 1}`,
                                String(Math.floor(Math.random() * 20) + 1)
                            ])
                        )
                    },
                    {
                        name: "服装鞋帽",
                        value: 120,
                        child: Object.fromEntries(
                            Array.from({length: 12}, (_, i) => [
                                `服装子类${i + 1}`,
                                String(Math.floor(Math.random() * 15) + 1)
                            ])
                        )
                    },
                    {
                        name: "家居用品",
                        value: 100,
                        child: Object.fromEntries(
                            Array.from({length: 10}, (_, i) => [
                                `家居子类${i + 1}`,
                                String(Math.floor(Math.random() * 12) + 1)
                            ])
                        )
                    }
                ],
                dimsCodes: ["主分类", "子分类"]
            },
            
            // 节点配置
            smallDataNode: {
                id: 'small-data-pie',
                code: 'PieChartWidget',
                widget: JSON.stringify({
                    beforeData: JSON.stringify({
                        xData: [{ label: '数值', alias: '数值', data: { code: 'value' }, func: { name: '求和' } }],
                        yData: [{ label: '类别', alias: '类别' }],
                        style: { title: { show: true, text: '少量数据测试' }, legend: { legend_pos: "top" } }
                    })
                })
            },
            
            largeDataNode: {
                id: 'large-data-pie',
                code: 'PieChartWidget',
                widget: JSON.stringify({
                    beforeData: JSON.stringify({
                        xData: [{ label: '销量', alias: '销量', data: { code: 'sales' }, func: { name: '求和' } }],
                        yData: [{ label: '产品', alias: '产品' }],
                        style: { title: { show: true, text: '大量数据测试' }, legend: { legend_pos: "top" } }
                    })
                })
            },
            
            nestedLargeDataNode: {
                id: 'nested-large-data-pie',
                code: 'RingPieChartWidget',
                widget: JSON.stringify({
                    beforeData: JSON.stringify({
                        xData: [{ label: 'value', alias: '销量', data: { code: 'value' }, func: { name: '求和' } }],
                        yData: [
                            { label: 'category', alias: '主分类' },
                            { label: 'subcategory', alias: '子分类' }
                        ],
                        style: { title: { show: true, text: '嵌套饼图大量数据' }, legend: { legend_pos: "top" } }
                    })
                })
            },
            
            fixedHeightNode: {
                id: 'fixed-height-pie',
                code: 'PieChartWidget',
                widget: JSON.stringify({
                    beforeData: JSON.stringify({
                        xData: [{ label: '销量', alias: '销量', data: { code: 'sales' }, func: { name: '求和' } }],
                        yData: [{ label: '产品', alias: '产品' }],
                        style: { title: { show: true, text: '固定高度测试' }, legend: { legend_pos: "top" } }
                    })
                })
            }
        }
    },
    
    mounted() {
        this.loadTestData();
    },
    
    methods: {
        loadTestData() {
            setTimeout(() => {
                // 加载少量数据
                this.sendDataToComponent('small-data-pie', { status: 0, data: this.smallData });
                
                // 加载大量数据
                this.sendDataToComponent('large-data-pie', { status: 0, data: this.largeData });
                
                // 加载嵌套大量数据
                this.sendDataToComponent('nested-large-data-pie', { status: 0, data: this.nestedLargeData });
                
                // 加载固定高度数据
                this.sendDataToComponent('fixed-height-pie', { status: 0, data: this.largeData });
                
                // 自动切换到表格模式进行测试
                setTimeout(() => {
                    this.switchAllToTableMode();
                }, 1000);
            }, 500);
        },
        
        sendDataToComponent(componentId, data) {
            const component = this.findPieCardComponent(componentId);
            if (component) {
                component.getMessage({ data: JSON.stringify(data) });
            }
        },
        
        findPieCardComponent(id) {
            const findComponent = (children) => {
                for (let child of children) {
                    if (child.$options.name === 'PieCard' && child.node && child.node.id === id) {
                        return child;
                    }
                    if (child.$children && child.$children.length > 0) {
                        const found = findComponent(child.$children);
                        if (found) return found;
                    }
                }
                return null;
            };
            return findComponent(this.$children);
        },
        
        switchAllToTableMode() {
            ['small-data-pie', 'large-data-pie', 'nested-large-data-pie', 'fixed-height-pie'].forEach(id => {
                const component = this.findPieCardComponent(id);
                if (component) {
                    component.switchDisplayMode('table');
                }
            });
        }
    }
}
</script>

<style scoped lang="less">
.scroll-test {
    padding: 20px;
    
    h2 {
        color: #303133;
        margin-bottom: 30px;
        text-align: center;
        border-bottom: 2px solid #409eff;
        padding-bottom: 10px;
    }
    
    .test-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 30px;
        
        .test-item {
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            padding: 15px;
            background-color: #fff;
            
            h3 {
                color: #606266;
                margin-bottom: 15px;
                font-size: 14px;
                text-align: center;
                background-color: #f5f7fa;
                padding: 8px;
                border-radius: 4px;
            }
            
            .chart-container {
                width: 100%;
                height: 350px;
                position: relative;
                border: 1px solid #e4e7ed;
                border-radius: 4px;
                
                &.small-container {
                    height: 200px;
                }
            }
        }
    }
    
    .feature-description {
        background-color: #f0f9ff;
        padding: 20px;
        border-radius: 8px;
        border-left: 4px solid #409eff;
        
        h3 {
            color: #409eff;
            margin-bottom: 15px;
        }
        
        ul {
            margin: 0;
            padding-left: 20px;
            
            li {
                color: #606266;
                margin-bottom: 8px;
                line-height: 1.5;
                
                &::marker {
                    color: #67c23a;
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .test-grid {
        grid-template-columns: 1fr;
    }
}
</style>
