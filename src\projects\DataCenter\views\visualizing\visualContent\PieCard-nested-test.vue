<template>
    <div class="nested-pie-test">
        <h2>嵌套饼图表格功能测试</h2>
        
        <div class="test-grid">
            <!-- 普通饼图测试 -->
            <div class="test-item">
                <h3>普通饼图</h3>
                <div class="chart-container">
                    <PieCard 
                        :node="normalPieNode" 
                        :id="'normal-pie-test'"
                        :isView="false"
                    />
                </div>
            </div>
            
            <!-- 嵌套饼图测试 -->
            <div class="test-item">
                <h3>嵌套饼图 (RingPieChartWidget)</h3>
                <div class="chart-container">
                    <PieCard 
                        :node="nestedPieNode" 
                        :id="'nested-pie-test'"
                        :isView="false"
                    />
                </div>
            </div>
        </div>
        
        <div class="data-preview">
            <h3>嵌套饼图数据格式：</h3>
            <pre>{{ JSON.stringify(nestedPieData, null, 2) }}</pre>
        </div>
        
        <div class="feature-description">
            <h3>嵌套饼图表格特性：</h3>
            <ul>
                <li>✅ 支持两层维度展示（外层 + 内层）</li>
                <li>✅ 外层汇总行：显示每个外层分类的总计</li>
                <li>✅ 内层明细行：显示每个外层分类下的子分类</li>
                <li>✅ 层级缩进：内层行使用缩进和树形符号</li>
                <li>✅ 差异化样式：外层行加粗，内层行缩进</li>
                <li>✅ 颜色指示器：只在外层汇总行显示</li>
                <li>✅ 百分比计算：基于总体数据计算占比</li>
            </ul>
        </div>
    </div>
</template>

<script>
import PieCard from './PieCard.vue'

export default {
    name: 'PieCardNestedTest',
    components: {
        PieCard
    },
    data() {
        return {
            // 普通饼图数据
            normalPieData: {
                columns: ["民族名称", "人数"],
                rows: [
                    { "民族名称": "汉族", "人数": 92 },
                    { "民族名称": "蒙古族", "人数": 5 },
                    { "民族名称": "满族", "人数": 2 },
                    { "民族名称": "回族", "人数": 1 }
                ],
                dimsCodes: ["民族名称"],
                formatMapping: {}
            },
            
            // 嵌套饼图数据
            nestedPieData: {
                columns: {
                    dims: ["zgxlmc", "mzmc"],
                    measures: "xbmc"
                },
                rows: [
                    {
                        name: "汉族",
                        value: 92,
                        child: {
                            "中等专科结业": "1",
                            "中等职业教育": "4",
                            "初中毕业": "31",
                            "初级中学教育": "13",
                            "大学普通班毕业": "1",
                            "中等专科毕业": "4",
                            "大学专科毕业": "4",
                            "小学肆业": "3",
                            "文盲或半文盲": "2",
                            "初中肆业": "3",
                            "大学本科毕业": "3",
                            "普通高级中学教育": "11",
                            "专科教育": "2",
                            "小学教育": "4",
                            "小学毕业": "5",
                            "技工学校毕业": "1"
                        }
                    },
                    {
                        name: "满族",
                        value: 2,
                        child: {
                            "技工学校毕业": "1",
                            "初中毕业": "1"
                        }
                    },
                    {
                        name: "回族",
                        value: 1,
                        child: {
                            "小学肆业": "1"
                        }
                    }
                ],
                dimsCodes: ["民族名称", "最高学历名称"]
            },
            
            // 普通饼图节点配置
            normalPieNode: {
                id: 'normal-pie-test',
                code: 'PieChartWidget',
                widget: JSON.stringify({
                    beforeData: JSON.stringify({
                        xData: [{
                            label: '人数',
                            alias: '人数',
                            data: { code: 'population', numberFormat: '#,##0' },
                            func: { name: '求和' }
                        }],
                        yData: [{
                            label: '民族名称',
                            alias: '民族'
                        }],
                        style: {
                            title: { show: true, text: '民族分布统计' },
                            legend: { legend_pos: "top", orient: "horizontal" }
                        }
                    })
                })
            },
            
            // 嵌套饼图节点配置
            nestedPieNode: {
                id: 'nested-pie-test',
                code: 'RingPieChartWidget',
                widget: JSON.stringify({
                    beforeData: JSON.stringify({
                        xData: [{
                            label: 'xbmc',
                            alias: '人数',
                            data: { code: 'xbmc', numberFormat: '#,##0' },
                            func: { name: '求和' }
                        }],
                        yData: [
                            {
                                label: 'mzmc',
                                alias: '民族名称'
                            },
                            {
                                label: 'zgxlmc',
                                alias: '最高学历名称'
                            }
                        ],
                        style: {
                            title: { show: true, text: '民族学历分布统计' },
                            legend: { legend_pos: "top", orient: "horizontal" }
                        }
                    })
                })
            }
        }
    },
    
    mounted() {
        // 加载测试数据
        this.loadTestData();
    },
    
    methods: {
        loadTestData() {
            setTimeout(() => {
                // 加载普通饼图数据
                const normalComponent = this.findPieCardComponent('normal-pie-test');
                if (normalComponent) {
                    normalComponent.getMessage({
                        data: JSON.stringify({
                            status: 0,
                            data: this.normalPieData
                        })
                    });
                }
                
                // 加载嵌套饼图数据
                const nestedComponent = this.findPieCardComponent('nested-pie-test');
                if (nestedComponent) {
                    nestedComponent.getMessage({
                        data: JSON.stringify({
                            status: 0,
                            data: this.nestedPieData
                        })
                    });
                }
            }, 1000);
        },
        
        findPieCardComponent(id) {
            const findComponent = (children) => {
                for (let child of children) {
                    if (child.$options.name === 'PieCard' && child.node && child.node.id === id) {
                        return child;
                    }
                    if (child.$children && child.$children.length > 0) {
                        const found = findComponent(child.$children);
                        if (found) return found;
                    }
                }
                return null;
            };
            return findComponent(this.$children);
        }
    }
}
</script>

<style scoped lang="less">
.nested-pie-test {
    padding: 20px;
    
    h2 {
        color: #303133;
        margin-bottom: 30px;
        text-align: center;
        border-bottom: 2px solid #409eff;
        padding-bottom: 10px;
    }
    
    .test-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 30px;
        
        .test-item {
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            padding: 15px;
            background-color: #fff;
            
            h3 {
                color: #606266;
                margin-bottom: 15px;
                font-size: 14px;
                text-align: center;
                background-color: #f5f7fa;
                padding: 8px;
                border-radius: 4px;
            }
            
            .chart-container {
                width: 100%;
                height: 400px;
                position: relative;
                border: 1px solid #e4e7ed;
                border-radius: 4px;
            }
        }
    }
    
    .data-preview {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
        
        h3 {
            color: #606266;
            margin-bottom: 10px;
        }
        
        pre {
            background-color: #fff;
            padding: 15px;
            border-radius: 4px;
            font-size: 12px;
            overflow-x: auto;
            color: #606266;
            border: 1px solid #e4e7ed;
        }
    }
    
    .feature-description {
        background-color: #f0f9ff;
        padding: 20px;
        border-radius: 8px;
        border-left: 4px solid #409eff;
        
        h3 {
            color: #409eff;
            margin-bottom: 15px;
        }
        
        ul {
            margin: 0;
            padding-left: 20px;
            
            li {
                color: #606266;
                margin-bottom: 8px;
                line-height: 1.5;
                
                &::marker {
                    color: #67c23a;
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .test-grid {
        grid-template-columns: 1fr;
    }
}
</style>
