<template>
    <div class="groupStatisSeniorList">
        <el-form label-width="120px" label-position="right">
            <!-- 自动刷新配置 -->
            <el-form-item>
                <div slot="label" class="tr vm" style="display: inline-block;">
                    <el-checkbox v-model="refresh.opened">{{refresh.label}}</el-checkbox>
                </div>
                <div>
                    <el-input :placeholder="refresh.placeholder"
                              v-model.trim.number="refresh.value"
                              @keyup.native="typeCheck(refresh.value)"
                              class="input-with-select">
                        <el-select class="ce-select_in" v-model="refresh.selectValue" slot="append">
                            <el-option v-for="opt in refresh.options" :key="opt.value" :label="opt.label"
                                       :value="opt.value"></el-option>
                        </el-select>
                    </el-input>
                    <span class="ce-tip">{{tip}}</span>
                </div>
            </el-form-item>

            <!-- 数据限制配置 -->
            <el-form-item label="最大显示行数">
                <el-input-number 
                    v-model="maxRows" 
                    :min="10" 
                    :max="10000" 
                    size="mini"
                    @change="onConfigChange">
                </el-input-number>
            </el-form-item>

            <!-- 空值处理 -->
            <el-form-item label="空值显示">
                <el-input 
                    v-model="nullValueDisplay" 
                    size="mini" 
                    placeholder="空值显示文本"
                    @change="onConfigChange">
                </el-input>
            </el-form-item>

            <!-- 零值处理 -->
            <el-form-item label="零值处理">
                <el-checkbox v-model="hideZeroValues" @change="onConfigChange">隐藏零值行</el-checkbox>
            </el-form-item>

            <!-- 性能优化 -->
            <el-form-item label="性能优化">
                <div>
                    <el-checkbox v-model="enableVirtualScroll" @change="onConfigChange">启用虚拟滚动</el-checkbox>
                    <div class="config-tip">大数据量时建议开启</div>
                </div>
                <div style="margin-top: 8px;">
                    <el-checkbox v-model="enableLazyLoad" @change="onConfigChange">启用懒加载</el-checkbox>
                    <div class="config-tip">分批加载数据</div>
                </div>
            </el-form-item>

            <!-- 导出配置 -->
            <el-form-item label="导出格式">
                <el-select v-model="defaultExportFormat" size="mini" style="width: 120px" @change="onConfigChange">
                    <el-option label="Excel (.xlsx)" value="xlsx"></el-option>
                    <el-option label="CSV (.csv)" value="csv"></el-option>
                    <el-option label="JSON (.json)" value="json"></el-option>
                </el-select>
            </el-form-item>

            <!-- 联动配置 -->
            <el-form-item :label="linkage.label">
                <el-button icon="el-icon-plus" size="mini" type="primary" @click="setRelation">{{linkage.btnTxt}}</el-button>
            </el-form-item>
        </el-form>
        <relation ref="relation" @storeRelation="storeRelation"></relation>
    </div>
</template>

<script src="./senior-list.js"></script>
<style scoped lang="less" src="./senior-list.less"></style>
