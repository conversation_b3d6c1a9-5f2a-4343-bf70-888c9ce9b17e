<template>
    <div class="tabCard chartCard" :class="'tab_'+node.id" v-loading="loading">
        <div class="listTitle" :style="[titleStyle , {'line-height' : titleH + 'px'}]" v-show="showTitle">{{title}}
        </div>
        <div class="ovh" :style="{'text-align' : tabAlign ,'height' :  tabH + 15 + 'px'}" v-if="!loading">
            <el-tabs v-model="activeName" class="ce-visualTabs" @tab-click="tabClick" :type="tabStyle">
                <el-tab-pane v-for="tab in tabsOpt" :key="tab.value" :label="tab.label" :name="tab.value"></el-tab-pane>
            </el-tabs>
        </div>
        <div class="ce-tab_charts" ref="tab_charts" :style="{'height': `calc( 100% - ${titleH + tabH + 15}px)`}"
             v-if="!loading"
             v-loading="loadChart">
            <div class="ce-tab_charts-win" v-for="tab in tabsOpt" :key="tab.value" v-show="activeName === tab.value"
                 @dragover.stop="allowDrop($event , tab)" @drop.stop="addNode($event , tab)">
                <grid-layout
                        :layout.sync="tab.nodes"
                        :col-num="colNum"
                        :row-height="rowNum"
                        :is-draggable="!isFullScreen"
                        :is-resizable="!isFullScreen"
                        :is-mirrored="mirrored"
                        :vertical-compact="verticalCompact"
                        :margin="gridMargin"
                        :use-css-transforms="useCssTransforms"
                        :prevent-collision="preventCollision"
                        :responsive="responsive"
                >
                    <grid-item v-for="grid_node in tab.nodes"
                               :x="grid_node.x"
                               :y="grid_node.y"
                               :w="grid_node.w"
                               :h="grid_node.h"
                               :i="grid_node.i"
                               :minW="grid_node.minW"
                               :minH="grid_node.minH"
                               :key="grid_node.i"
                               :dragAllowFrom="dragCss"
                               @resize="resizeEvent"
                               @move="moveEvent"
                               @resized="resizedEvent"
                               @moved="movedEvent"
                    >
                        <VisualNode
                                :isView="isView"
                                :isPreview="isFullScreen"
                                :class="{'visualNode_hover' : !isFullScreen }"
                                :label="grid_node.label" :node="grid_node"
                                :id="grid_node.i"
                                :isTab="true"
                                @copyPanel="copyPanel"
                                @getSql="getSql"
                                @deleteNode="deleteNode($event , tab.value , grid_node.i)"
                                @editNode="editNode">
                            <template>
                                <HistogramCard @setLinkage="setLinkage" :isView="isView"
                                               :ref="'chart'+id + grid_node.id"
                                               :node="grid_node"
                                               :id="grid_node.id"
                                               v-if="grid_node.code === 'BarChartWidget'|| grid_node.code === 'StackBarChartWidget'|| grid_node.code === 'TransverseBarChartWidget'|| grid_node.code === 'TransverseStackBarChartWidget'"></HistogramCard>
                                <LineCard @setLinkage="setLinkage" :isView="isView" :ref="'chart'+id + grid_node.id"
                                          :node="grid_node"
                                          :id="grid_node.id"
                                          v-if="grid_node.code === 'LineChartWidget' || grid_node.code === 'AreaGraphWidget'"></LineCard>
                                <PieCard @setLinkage="setLinkage" :isView="isView" :ref="'chart'+id + grid_node.id"
                                         :node="grid_node"
                                         :id="grid_node.id"
                                         v-if="grid_node.code === 'PieChartWidget' || grid_node.code === 'RingPieChartWidget'"></PieCard>
                                <TableCard @setLinkage="setLinkage" :isView="isView" :ref="'chart'+id + grid_node.id"
                                           :node="grid_node"
                                           :id="grid_node.id"
                                           v-if="grid_node.code === 'TableChartWidget'"></TableCard>
                                <MapCard @setLinkage="setLinkage" :isView="isView" :ref="'chart'+ id + grid_node.id"
                                         :node="grid_node"
                                         :id="grid_node.id"
                                         v-if="grid_node.code === 'MapChartWidget' || grid_node.code === 'ColourMap'"></MapCard>
                                <indicator
                                        :isView="isView"
                                        :ref="'chart'+id+ grid_node.id"
                                        :node="grid_node"
                                        :id="grid_node.id"
                                        v-if="grid_node.code === 'IndicatorCardWidget'"
                                ></indicator>
                                <heat-map-card @setLinkage="setLinkage"
                                         :isView="isView"
                                         :ref="'chart'+id+ grid_node.id"
                                         :node="grid_node"
                                         :id="grid_node.id"
                                         v-if="grid_node.code === 'HeatMap'"
                                >
                                </heat-map-card>
                                <wordCloud @setLinkage="setLinkage"
                                           :isView="isView"
                                           :ref="'chart'+id+ grid_node.id"
                                           :node="grid_node"
                                           :id="grid_node.id"
                                           v-if="grid_node.code === 'WordCloudWidget'"
                                >
                                </wordCloud>
                                <bubble-map
                                        @setLinkage="setLinkage"
                                        :isView="isView"
                                        :ref="'chart'+id+ grid_node.id"
                                        :node="grid_node"
                                        :id="grid_node.id"
                                        v-if="grid_node.code === 'BubbleMap'"
                                ></bubble-map>
                                <pgisCard
                                        :isView="isView"
                                        :ref="'chart'+id+ grid_node.id"
                                        :node="grid_node"
                                        :id="grid_node.id"
                                        v-if="grid_node.code === 'PGISWidget'"
                                        @setLinkage="setLinkage"
                                ></pgisCard>
                                <combination
                                        :isView="isView"
                                        :ref="'chart'+id+ grid_node.id"
                                        :node="grid_node"
                                        :id="grid_node.id"
                                        v-if="grid_node.code === 'CombinationWidget'"
                                        @setLinkage="setLinkage"
                                ></combination>
                                <Radar
                                        :isView="isView"
                                        :ref="'chart'+id+ grid_node.id"
                                        :node="grid_node"
                                        :id="grid_node.id"
                                        v-if="grid_node.code === 'RadarWidget'"
                                        @setLinkage="setLinkage"
                                ></Radar>
                                <SearchCard :isView="isView" :ref="'chart' + id +grid_node.id" :node="grid_node"
                                            :nodeList="nodeList"
                                            :id="grid_node.id"
                                            v-if="grid_node.code === 'SelectWidget'"
                                            @getFormList="getFormList"
                                            @filterAssociationCharts="filterAssociationCharts"></SearchCard>
                                <TextCard :ref="'chart' + id + grid_node.id" :node="grid_node" :id="grid_node.id"
                                          v-if="grid_node.code === 'TextWidget'"></TextCard>
                            </template>
                        </VisualNode>
                    </grid-item>
                </grid-layout>
                <div v-if="tab.nodes.length === 0" class="ce-drop_area"><span v-html="drop_tip"></span></div>
            </div>

            <div v-if="!tabsOpt.length" class="ce-data-none">{{data_none}}</div>
        </div>
    </div>
</template>

<script src="./tab-card.js"></script>
<style scoped lang="less" src="../css/chart.less"></style>
<style scoped lang="less" src="./tab-card.less"></style>
