/**
 * 分组统计表格样式配置样式
 */
.groupStatisStyleList {
    .el-collapse {
        border: none;
        
        .el-collapse-item {
            .el-collapse-item__header {
                background-color: #f8f9fa;
                border-bottom: 1px solid #e4e7ed;
                padding-left: 15px;
                font-size: 13px;
                font-weight: 500;
                color: #303133;
                
                &:hover {
                    background-color: #ecf5ff;
                }
                
                .el-icon-arrow-right {
                    margin-right: 8px;
                    transition: transform 0.3s;
                }
                
                &.is-active {
                    .el-icon-arrow-right {
                        transform: rotate(90deg);
                    }
                }
            }
            
            .el-collapse-item__wrap {
                border-bottom: 1px solid #e4e7ed;
                
                .el-collapse-item__content {
                    padding: 15px;
                    background-color: #fff;
                }
            }
        }
    }
    
    // 表格样式特定配置
    .table-style-config {
        .config-row {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            
            .config-label {
                width: 80px;
                font-size: 12px;
                color: #606266;
                margin-right: 10px;
            }
            
            .config-control {
                flex: 1;
            }
        }
    }
    
    // 列样式特定配置
    .column-style-config {
        .width-config {
            display: flex;
            align-items: center;
            gap: 10px;
            
            .width-mode-select {
                width: 120px;
            }
            
            .width-value-input {
                width: 80px;
            }
        }
    }
    
    // 字段设置特定配置
    .field-setting-config {
        .field-item {
            padding: 8px 12px;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            margin-bottom: 8px;
            background-color: #f8f9fa;
            
            .field-name {
                font-size: 12px;
                color: #303133;
                font-weight: 500;
                margin-bottom: 5px;
            }
            
            .field-alias {
                font-size: 11px;
                color: #909399;
            }
        }
    }
}
