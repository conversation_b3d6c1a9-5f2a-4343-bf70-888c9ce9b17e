# PieCard 图例遮挡问题修复

## 问题描述
饼图组件中标题和图例位置重叠，导致图例被遮挡，影响用户体验。

## 问题原因分析
1. **标题位置**: 原始设置为 `top: 'top'`，占用了顶部空间
2. **图例位置**: 默认设置为 `top: 26`，与标题位置冲突
3. **饼图中心**: 位置为 `center: ['50%', '55%']`，没有为标题和图例预留足够空间
4. **切换按钮**: 位置可能与标题重叠

## 修复方案

### 1. 标题位置优化
```javascript
// 修改前
title: {
    show: true,
    top: 'top',
    left: 'center'
}

// 修改后
title: {
    show: true,
    top: 10,  // 固定像素值，确保位置准确
    left: 'center'
}
```

### 2. 重写 setTitle 方法
```javascript
setTitle(title) {
    const vm = this, {extend} = this;
    extend.title.show = title.show;
    extend.title.text = title.text;
    
    if (title.custom_style === 'custom') {
        // 自定义样式处理
        extend.title.top = 15;
    } else {
        // 默认样式
        extend.title.top = 15;
        extend.title.textStyle = {
            color: '#666',
            fontSize: 16
        };
    }
}
```

### 3. 重写 legendAuto 方法
```javascript
legendAuto(legend) {
    // 根据是否有标题动态调整图例位置
    if (legend.legend_pos === 'top') {
        left = 'center';
        top = vm.extend.title.show ? 50 : 30; // 有标题时下移更多
    }
    // ... 其他位置处理
}
```

### 4. 饼图中心位置调整
```javascript
// 普通饼图
center: ['50%', '60%']  // 从 55% 调整到 60%

// 环形饼图
center: ['50%', '60%']  // 内外环都调整到 60%
```

### 5. 切换按钮位置优化
```css
.chart-toggle-buttons {
    position: absolute;
    top: 5px;    /* 从 10px 调整到 5px */
    right: 10px;
    z-index: 10;
}
```

## 修复效果

### 布局间距
- **标题位置**: `top: 15px`
- **图例位置**: 
  - 有标题时: `top: 50px`
  - 无标题时: `top: 30px`
- **饼图中心**: `center: ['50%', '60%']`
- **切换按钮**: `top: 5px`

### 适配不同图例位置
- ✅ **上方图例**: 动态调整与标题的间距
- ✅ **右侧图例**: 垂直居中，不受标题影响
- ✅ **底部图例**: 位于底部，不与标题冲突
- ✅ **左侧图例**: 垂直居中，不受标题影响

### 兼容性保证
- ✅ 普通饼图和环形饼图都适配
- ✅ 有标题和无标题场景都处理
- ✅ 自定义标题样式兼容
- ✅ 表格切换功能不受影响

## 测试验证

### 测试用例
1. **有标题 + 上方图例**: 验证间距是否合适
2. **无标题 + 上方图例**: 验证图例位置是否正确
3. **有标题 + 右侧图例**: 验证右侧布局
4. **有标题 + 底部图例**: 验证底部布局

### 测试文件
- `PieCard-layout-test.vue`: 布局修复测试页面
- 包含4个不同配置的测试用例
- 可视化验证修复效果

## 代码变更总结

### 新增方法
- `legendAuto()`: 重写图例自动布局
- `setTitle()`: 重写标题设置

### 修改配置
- `extend.title.top`: 10 → 15
- `extend.legend.top`: 26 → 动态计算(30/50)
- `series.center`: ['50%', '55%'] → ['50%', '60%']
- `.chart-toggle-buttons top`: 10px → 5px

### 优化逻辑
- 根据标题显示状态动态调整图例位置
- 统一饼图中心位置，为标题和图例预留空间
- 优化切换按钮位置，避免与标题重叠

## 使用说明

修复后的组件使用方式不变，但布局更加合理：

```vue
<PieCard 
    :node="nodeConfig" 
    :id="'pie-card-id'"
    :isView="false"
/>
```

组件会自动根据标题和图例配置调整布局，确保所有元素都能正确显示，不会出现遮挡问题。
