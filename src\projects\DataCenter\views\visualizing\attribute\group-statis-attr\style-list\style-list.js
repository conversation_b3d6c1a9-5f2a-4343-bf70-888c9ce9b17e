import chartStyle from "../../chart-style"
import {attrStyle} from "../../attr-mixins/attr-style";

/**
 * 分组统计表格样式配置组件
 * 参考饼图样式配置实现
 */
export default {
    name: "groupStatisStyleList",
    components: {
        chartStyle
    },
    mixins: [attrStyle],
    props: {
        node: Object,
        dir_dataSetId: String
    },
    data() {
        return {
            activeTabs: ["title", "tableStyle", "columnStyle", "fieldSetting"],
            styleList: {
                title: {
                    label: "标题内容",
                    settingForm: {
                        show: {
                            label: "显示标题",
                            type: "checkbox",
                        },
                        custom_style: {
                            label: "字体样式",
                            type: "sty_radio"
                        },
                        fontFamily: {
                            type: "select_font",
                            isLine: true
                        },
                        fontSize: {
                            type: "select_size",
                            isLine: true
                        },
                        fontWeight: {
                            type: "select_w",
                            isLine: true
                        },
                        fontStyle: {
                            type: "checkBtn",
                            isLine: true
                        },
                        color: {
                            type: "font_color",
                            isLine: true
                        },
                        align: {
                            type: "font_align",
                            isLine: true
                        },
                        text: {
                            type: "form_input",
                            pos: "right",
                            label: "标题名称",
                            maxlength: 30,
                            width: "76px",
                            placeholder: "请输入标题名称(限30个字符)"
                        },
                    },
                    form: {
                        show: true,
                        custom_style: "default",
                        fontFamily: "sans-serif",
                        fontSize: "12px",
                        fontWeight: "normal",
                        fontStyle: [],
                        color: "#666",
                        text: "",
                        align: "center"
                    }
                },
                tableStyle: {
                    label: "表格样式",
                    settingForm: {
                        tableSize: {
                            label: "表格尺寸",
                            type: "form_select",
                            width: "120px",
                            pos: "right",
                            options: [
                                {value: "mini", label: "迷你"},
                                {value: "small", label: "小型"},
                                {value: "medium", label: "中等"},
                                {value: "large", label: "大型"}
                            ]
                        },
                        showBorder: {
                            label: "显示边框",
                            type: "checkbox",
                        },
                        showStripe: {
                            label: "斑马纹",
                            type: "checkbox",
                        },
                        headerBgColor: {
                            label: "表头背景色",
                            type: "font_color",
                            isLine: true
                        },
                        headerTextColor: {
                            label: "表头文字颜色",
                            type: "font_color",
                            isLine: true
                        }
                    },
                    form: {
                        tableSize: "small",
                        showBorder: true,
                        showStripe: true,
                        headerBgColor: "#f5f7fa",
                        headerTextColor: "#303133"
                    }
                },
                columnStyle: {
                    label: "列样式",
                    settingForm: {
                        columnWidthMode: {
                            label: "列宽模式",
                            type: "form_select",
                            width: "120px",
                            pos: "right",
                            options: [
                                {value: "auto", label: "自适应"},
                                {value: "fixed", label: "固定宽度"}
                            ]
                        },
                        defaultColumnWidth: {
                            label: "默认列宽",
                            type: "number_slider",
                            step: 10,
                            min: 80,
                            max: 500,
                        },
                        textAlign: {
                            label: "文字对齐",
                            type: "form_select",
                            width: "120px",
                            pos: "right",
                            options: [
                                {value: "left", label: "左对齐"},
                                {value: "center", label: "居中"},
                                {value: "right", label: "右对齐"}
                            ]
                        },
                        showPagination: {
                            label: "显示分页",
                            type: "checkbox",
                        },
                        pageSize: {
                            label: "每页显示",
                            type: "form_select",
                            width: "120px",
                            pos: "right",
                            options: [
                                {value: 10, label: "10条"},
                                {value: 20, label: "20条"},
                                {value: 50, label: "50条"},
                                {value: 100, label: "100条"}
                            ]
                        },
                        decimalPlaces: {
                            label: "数值小数位",
                            type: "number_slider",
                            step: 1,
                            min: 0,
                            max: 6,
                        },
                        useThousandSeparator: {
                            label: "千分位分隔符",
                            type: "checkbox",
                        }
                    },
                    form: {
                        columnWidthMode: "auto",
                        defaultColumnWidth: 120,
                        textAlign: "left",
                        showPagination: true,
                        pageSize: 20,
                        decimalPlaces: 2,
                        useThousandSeparator: true
                    }
                },
                fieldSetting: {
                    label: "字段设置",
                    settingForm: {
                        alias: {
                            label: "别名",
                            type: "field_alias",
                            maxlength: 100,
                            width: "120px",
                            pos: "right",
                            placeholder: "请输入别名(限100个字符)"
                        }
                    },
                    form: {
                        options: [],
                    }
                }
            },
            emitStyle: {}
        }
    },
    methods: {
        /**
         * 获取所有样式配置
         * @returns {Object}
         */
        getAllStyle() {
            const {emitStyle} = this;
            let styles = {};
            for (let k in emitStyle) {
                styles[k] = emitStyle[k];
            }
            return styles;
        },

        /**
         * 设置分组统计轴名称
         * @param {Object} node - 节点对象
         * @param {String} type - 类型
         * @param {Array} lists - 列表数据
         */
        setGroupStatisAxisName(node, type, lists) {
            // 处理分组字段的轴名称设置
            console.log('设置分组统计轴名称:', node, type, lists);
        },

        /**
         * 初始化数值
         */
        initValue() {
            const vm = this;
            vm.$nextTick(() => {
                vm.node.setData = true;
                vm.styleList.title.form.text = vm.node.label;
                if (!vm.node.widget) return;
                let {style} = JSON.parse(JSON.parse(vm.node.widget).beforeData);
                let {title, tableStyle, columnStyle, fieldSetting} = vm.copyArrayObj(style);
                
                title ? vm.emitStyle.title = vm.styleList.title.form = title : vm.emitStyle.title = vm.styleList.title.form;
                tableStyle ? vm.emitStyle.tableStyle = vm.styleList.tableStyle.form = tableStyle : vm.emitStyle.tableStyle = vm.styleList.tableStyle.form;
                columnStyle ? vm.emitStyle.columnStyle = vm.styleList.columnStyle.form = columnStyle : vm.emitStyle.columnStyle = vm.styleList.columnStyle.form;
                fieldSetting ? vm.emitStyle.fieldSetting = vm.styleList.fieldSetting.form = fieldSetting : vm.emitStyle.fieldSetting = vm.styleList.fieldSetting.form;
                
                vm.node.setData = false;
            })
        }
    }
}
