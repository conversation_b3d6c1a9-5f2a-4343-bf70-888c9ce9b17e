<template>
    <div class="chartCard groupStatisCard" :class="{'pt30': showDrill}">
        <div :style="{'height': showDrill ? drillHeight : height}">
            <!-- 表格标题 -->
            <div class="table-header" v-if="showTitle">
                <h3 class="table-title" :style="titleStyle">{{ titleText || '分组统计表' }}</h3>
            </div>

            <!-- 表格内容 -->
            <div class="table-container" v-loading="loading">
                <el-table
                    ref="groupStatisTable"
                    :data="displayData"
                    :size="tableSize"
                    :border="showBorder"
                    :stripe="showStripe"
                    :header-cell-style="headerCellStyle"
                    :cell-style="cellStyle"
                    :max-height="maxTableHeight"
                    @sort-change="handleSortChange"
                    @filter-change="handleFilterChange"
                    @row-click="handleRowClick">
                    
                    <!-- 分组字段列 -->
                    <el-table-column
                        v-for="(field, index) in groupColumns"
                        :key="`group_${index}`"
                        :prop="field.prop"
                        :label="field.label"
                        :width="getColumnWidth(field)"
                        :align="textAlign"
                        :sortable="enableSort"
                        :filters="getColumnFilters(field)"
                        :filter-method="enableFilter ? filterMethod : null"
                        show-overflow-tooltip>
                        <template slot-scope="scope">
                            <span :style="getCellTextStyle(scope.row, field.prop)">
                                {{ formatCellValue(scope.row[field.prop], field.type) }}
                            </span>
                        </template>
                    </el-table-column>

                    <!-- 统计字段列 -->
                    <el-table-column
                        v-for="(stat, index) in statisticsColumns"
                        :key="`stat_${index}`"
                        :prop="stat.prop"
                        :label="stat.label"
                        :width="getColumnWidth(stat)"
                        :align="textAlign"
                        :sortable="enableSort"
                        show-overflow-tooltip>
                        <template slot-scope="scope">
                            <span :style="getCellTextStyle(scope.row, stat.prop)" class="stat-value">
                                {{ formatStatValue(scope.row[stat.prop], stat.type) }}
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <!-- 分页组件 -->
            <div class="table-pagination" v-if="showPagination && totalCount > pageSize">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="pageSize"
                    :total="totalCount"
                    layout="total, sizes, prev, pager, next, jumper">
                </el-pagination>
            </div>

            <!-- 空数据提示 -->
            <div class="empty-data" v-if="!loading && displayData.length === 0">
                <el-empty description="暂无数据" :image-size="100"></el-empty>
            </div>
        </div>
        
        <!-- 钻取提示 -->
        <div v-if="showDrill" class="chartTip"
             @click="backToDims"
             :title="dimsName">
            <i class="el-icon-arrow-left"></i>{{dimsName}}
        </div>
    </div>
</template>

<script>
import {globalBus} from "@/api/globalBus";
import {chartMxins} from "./chart-mixins/chart-mixins";

/**
 * 分组统计表格可视化组件
 * 参考饼图模式实现的表格展示组件
 */
export default {
    name: "GroupStatisCard",
    mixins: [chartMxins],
    data() {
        return {
            // 表格数据
            tableData: [],
            displayData: [],
            
            // 分页配置
            currentPage: 1,
            pageSize: 20,
            totalCount: 0,
            
            // 表格配置
            showTitle: true,
            titleText: '',
            tableSize: 'small',
            showBorder: true,
            showStripe: true,
            showPagination: true,
            maxTableHeight: 400,
            
            // 样式配置
            headerBgColor: '#f5f7fa',
            headerTextColor: '#303133',
            textAlign: 'left',
            columnWidthMode: 'auto',
            defaultColumnWidth: 120,
            
            // 数据配置
            groupColumns: [],
            statisticsColumns: [],
            decimalPlaces: 2,
            useThousandSeparator: true,
            nullValueDisplay: '-',
            hideZeroValues: false,
            
            // 交互配置
            enableSort: true,
            enableFilter: true,
            
            // 排序和筛选
            currentSort: {},
            currentFilters: {},
            
            // 扩展配置
            extend: {
                title: {
                    show: true,
                    top: 'top',
                    left: 'center',
                    textStyle: {
                        color: '#666'
                    }
                }
            }
        };
    },
    computed: {
        /**
         * 标题样式
         */
        titleStyle() {
            return {
                color: this.headerTextColor,
                textAlign: this.textAlign,
                fontSize: '16px',
                fontWeight: '600',
                margin: '0 0 16px 0'
            };
        },

        /**
         * 表头单元格样式
         */
        headerCellStyle() {
            return {
                backgroundColor: this.headerBgColor,
                color: this.headerTextColor,
                fontWeight: '600',
                textAlign: this.textAlign
            };
        },

        /**
         * 普通单元格样式
         */
        cellStyle() {
            return {
                textAlign: this.textAlign
            };
        }
    },
    methods: {
        /**
         * 预览方法 - 系统要求的方法
         */
        preview() {
            this.updateChartData();
        },

        /**
         * 更新图表数据
         */
        updateChartData() {
            const vm = this;
            if (!vm.chartData || !vm.chartData.groupFields) {
                return;
            }
            
            vm.loading = true;
            
            try {
                // 更新配置
                vm.updateConfig();
                
                // 处理数据
                vm.processData();
                
                // 更新显示数据
                vm.updateDisplayData();
                
            } catch (error) {
                console.error('更新图表数据失败:', error);
            } finally {
                vm.loading = false;
            }
        },

        /**
         * 更新配置
         */
        updateConfig() {
            const vm = this;
            const data = vm.chartData;

            // 更新表格样式
            if (data.tableStyle) {
                const style = data.tableStyle;
                vm.tableSize = style.tableSize || 'small';
                vm.showBorder = style.showBorder !== false;
                vm.showStripe = style.showStripe !== false;
                vm.headerBgColor = style.headerBgColor || '#f5f7fa';
                vm.headerTextColor = style.headerTextColor || '#303133';
            }

            // 更新列样式
            if (data.columnStyle) {
                const columnStyle = data.columnStyle;
                vm.columnWidthMode = columnStyle.columnWidthMode || 'auto';
                vm.defaultColumnWidth = columnStyle.defaultColumnWidth || 120;
                vm.textAlign = columnStyle.textAlign || 'left';
                vm.showPagination = columnStyle.showPagination !== false;
                vm.pageSize = columnStyle.pageSize || 20;
                vm.decimalPlaces = columnStyle.decimalPlaces || 2;
                vm.useThousandSeparator = columnStyle.useThousandSeparator !== false;
            }

            // 更新高级配置
            if (data.dataLimit) {
                const dataLimit = data.dataLimit;
                vm.nullValueDisplay = dataLimit.nullValueDisplay || '-';
                vm.hideZeroValues = dataLimit.hideZeroValues || false;
            }
        },

        /**
         * 处理数据
         */
        processData() {
            const vm = this;
            const data = vm.chartData;

            // 设置分组字段
            vm.groupColumns = vm.generateGroupColumns(data);

            // 设置统计列
            vm.statisticsColumns = vm.generateStatisticsColumns(data);

            // 处理表格数据
            vm.tableData = vm.generateTableData(data);
            vm.totalCount = vm.tableData.length;
        },

        /**
         * 生成分组列配置
         * @param {Object} data - 数据配置
         * @returns {Array}
         */
        generateGroupColumns(data) {
            const columns = [];
            if (data.groupFields && data.groupFields.length > 0) {
                data.groupFields.forEach(field => {
                    columns.push({
                        prop: field,
                        label: this.getFieldLabel(field),
                        type: this.getFieldType(field)
                    });
                });
            }
            return columns;
        },

        /**
         * 生成统计列配置
         * @param {Object} data - 数据配置
         * @returns {Array}
         */
        generateStatisticsColumns(data) {
            const columns = [];
            const statisticsTypes = data.statisticsTypes || ['count'];
            const statisticsFields = data.statisticsFields || [];

            statisticsTypes.forEach(type => {
                if (type === 'count') {
                    columns.push({
                        prop: 'count_value',
                        label: '计数',
                        type: 'count'
                    });
                } else {
                    statisticsFields.forEach(field => {
                        columns.push({
                            prop: `${field}_${type}`,
                            label: `${this.getFieldLabel(field)}(${this.getStatTypeLabel(type)})`,
                            type: type
                        });
                    });
                }
            });

            return columns;
        },

        /**
         * 生成表格数据
         * @param {Object} data - 原始数据
         * @returns {Array}
         */
        generateTableData(data) {
            // 这里应该根据实际的数据源和分组逻辑来生成数据
            // 暂时返回模拟数据
            const mockData = [
                {
                    region: '华北',
                    category: '电子产品',
                    count_value: 150,
                    amount_sum: 1500.50,
                    amount_avg: 10.00
                },
                {
                    region: '华北',
                    category: '服装',
                    count_value: 200,
                    amount_sum: 2000.75,
                    amount_avg: 10.00
                },
                {
                    region: '华南',
                    category: '电子产品',
                    count_value: 100,
                    amount_sum: 800.25,
                    amount_avg: 8.00
                }
            ];

            return this.hideZeroValues ?
                mockData.filter(row => !this.isZeroRow(row)) :
                mockData;
        },

        /**
         * 判断是否为零值行
         * @param {Object} row - 数据行
         * @returns {Boolean}
         */
        isZeroRow(row) {
            return this.statisticsColumns.some(col => {
                const value = row[col.prop];
                return value === 0 || value === '0';
            });
        },

        /**
         * 更新显示数据
         */
        updateDisplayData() {
            const start = (this.currentPage - 1) * this.pageSize;
            const end = start + this.pageSize;
            this.displayData = this.tableData.slice(start, end);
        },

        /**
         * 获取字段标签
         * @param {String} field - 字段名
         * @returns {String}
         */
        getFieldLabel(field) {
            // 这里应该从数据源获取字段的显示名称
            return field;
        },

        /**
         * 获取字段类型
         * @param {String} field - 字段名
         * @returns {String}
         */
        getFieldType(field) {
            // 这里应该从数据源获取字段类型
            return 'string';
        },

        /**
         * 获取统计类型标签
         * @param {String} type - 统计类型
         * @returns {String}
         */
        getStatTypeLabel(type) {
            const labels = {
                'count': '计数',
                'sum': '求和',
                'avg': '平均值',
                'max': '最大值',
                'min': '最小值'
            };
            return labels[type] || type;
        },

        /**
         * 获取列宽
         * @param {Object} column - 列配置
         * @returns {String|Number}
         */
        getColumnWidth(column) {
            if (this.columnWidthMode === 'fixed') {
                return this.defaultColumnWidth;
            }
            return undefined; // 自适应
        },

        /**
         * 格式化单元格值
         * @param {*} value - 原始值
         * @param {String} type - 字段类型
         * @returns {String}
         */
        formatCellValue(value, type) {
            if (value === null || value === undefined || value === '') {
                return this.nullValueDisplay;
            }
            return String(value);
        },

        /**
         * 格式化统计值
         * @param {*} value - 原始值
         * @param {String} type - 统计类型
         * @returns {String}
         */
        formatStatValue(value, type) {
            if (value === null || value === undefined || value === '') {
                return this.nullValueDisplay;
            }

            const numValue = Number(value);
            if (isNaN(numValue)) {
                return String(value);
            }

            // 格式化数值
            let formatted = numValue.toFixed(this.decimalPlaces);

            // 添加千分位分隔符
            if (this.useThousandSeparator) {
                formatted = this.addThousandSeparator(formatted);
            }

            return formatted;
        },

        /**
         * 添加千分位分隔符
         * @param {String} value - 数值字符串
         * @returns {String}
         */
        addThousandSeparator(value) {
            const parts = value.split('.');
            parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            return parts.join('.');
        },

        /**
         * 获取单元格文本样式
         * @param {Object} row - 行数据
         * @param {String} prop - 属性名
         * @returns {Object}
         */
        getCellTextStyle(row, prop) {
            return {};
        },

        /**
         * 获取列筛选选项
         * @param {Object} field - 字段配置
         * @returns {Array}
         */
        getColumnFilters(field) {
            if (!this.enableFilter) {
                return [];
            }

            // 从数据中提取唯一值作为筛选选项
            const uniqueValues = [...new Set(this.tableData.map(row => row[field.prop]))];
            return uniqueValues.map(value => ({
                text: this.formatCellValue(value, field.type),
                value: value
            }));
        },

        /**
         * 筛选方法
         * @param {*} value - 筛选值
         * @param {Object} row - 行数据
         * @param {Object} column - 列配置
         * @returns {Boolean}
         */
        filterMethod(value, row, column) {
            return row[column.property] === value;
        },

        /**
         * 处理排序变化
         * @param {Object} sortInfo - 排序信息
         */
        handleSortChange(sortInfo) {
            this.currentSort = sortInfo;
            console.log('排序变化:', sortInfo);
        },

        /**
         * 处理筛选变化
         * @param {Object} filters - 筛选信息
         */
        handleFilterChange(filters) {
            this.currentFilters = filters;
            console.log('筛选变化:', filters);
        },

        /**
         * 处理每页大小变化
         * @param {Number} size - 每页大小
         */
        handleSizeChange(size) {
            this.pageSize = size;
            this.currentPage = 1;
            this.updateDisplayData();
        },

        /**
         * 处理当前页变化
         * @param {Number} page - 当前页
         */
        handleCurrentChange(page) {
            this.currentPage = page;
            this.updateDisplayData();
        },

        /**
         * 处理行点击事件
         * @param {Object} row - 行数据
         * @param {Object} column - 列配置
         * @param {Event} event - 事件对象
         */
        handleRowClick(row, column, event) {
            // 处理行点击联动
            console.log('行点击:', row, column);
        }
    }
}
</script>

<style scoped lang="less">
.groupStatisCard {
    .table-header {
        padding: 16px 20px 0;
        
        .table-title {
            margin: 0 0 16px 0;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
        }
    }
    
    .table-container {
        padding: 0 20px;
        
        .el-table {
            .stat-value {
                font-weight: 500;
                color: #409eff;
            }
        }
    }
    
    .table-pagination {
        padding: 16px 20px;
        display: flex;
        justify-content: center;
    }
    
    .empty-data {
        padding: 40px 20px;
        text-align: center;
    }
}
</style>
