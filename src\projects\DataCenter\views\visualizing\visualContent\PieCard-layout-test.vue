<template>
    <div class="layout-test">
        <h2>饼图布局修复测试</h2>
        
        <div class="test-grid">
            <!-- 测试1: 有标题的饼图 -->
            <div class="test-item">
                <h3>测试1: 有标题 + 上方图例</h3>
                <div class="chart-container">
                    <PieCard 
                        :node="withTitleNode" 
                        :id="'with-title-pie'"
                        :isView="false"
                    />
                </div>
            </div>
            
            <!-- 测试2: 无标题的饼图 -->
            <div class="test-item">
                <h3>测试2: 无标题 + 上方图例</h3>
                <div class="chart-container">
                    <PieCard 
                        :node="noTitleNode" 
                        :id="'no-title-pie'"
                        :isView="false"
                    />
                </div>
            </div>
            
            <!-- 测试3: 右侧图例 -->
            <div class="test-item">
                <h3>测试3: 有标题 + 右侧图例</h3>
                <div class="chart-container">
                    <PieCard 
                        :node="rightLegendNode" 
                        :id="'right-legend-pie'"
                        :isView="false"
                    />
                </div>
            </div>
            
            <!-- 测试4: 底部图例 -->
            <div class="test-item">
                <h3>测试4: 有标题 + 底部图例</h3>
                <div class="chart-container">
                    <PieCard 
                        :node="bottomLegendNode" 
                        :id="'bottom-legend-pie'"
                        :isView="false"
                    />
                </div>
            </div>
        </div>
        
        <div class="test-description">
            <h3>修复内容：</h3>
            <ul>
                <li>✅ 标题位置调整为 top: 15px</li>
                <li>✅ 图例位置：有标题时 top: 50px，无标题时 top: 30px</li>
                <li>✅ 饼图中心位置调整为 center: ['50%', '60%']</li>
                <li>✅ 切换按钮位置调整为 top: 5px</li>
                <li>✅ 重写 legendAuto 和 setTitle 方法</li>
            </ul>
        </div>
    </div>
</template>

<script>
import PieCard from './PieCard.vue'

export default {
    name: 'PieCardLayoutTest',
    components: {
        PieCard
    },
    data() {
        return {
            // 测试数据
            testData: {
                columns: ["类别", "数值"],
                rows: [
                    { "类别": "类别A", "数值": 35 },
                    { "类别": "类别B", "数值": 25 },
                    { "类别": "类别C", "数值": 20 },
                    { "类别": "类别D", "数值": 15 },
                    { "类别": "类别E", "数值": 5 }
                ],
                dimsCodes: ["类别"],
                formatMapping: {}
            },
            
            // 有标题的节点
            withTitleNode: {
                id: 'with-title-pie',
                code: 'PieChartWidget',
                widget: JSON.stringify({
                    beforeData: JSON.stringify({
                        xData: [{ label: '数值', alias: '数值', data: { code: 'value' }, func: { name: '求和' } }],
                        yData: [{ label: '类别', alias: '类别' }],
                        style: {
                            title: { show: true, text: '这是一个测试标题' },
                            legend: { legend_pos: "top", orient: "horizontal" }
                        }
                    })
                })
            },
            
            // 无标题的节点
            noTitleNode: {
                id: 'no-title-pie',
                code: 'PieChartWidget',
                widget: JSON.stringify({
                    beforeData: JSON.stringify({
                        xData: [{ label: '数值', alias: '数值', data: { code: 'value' }, func: { name: '求和' } }],
                        yData: [{ label: '类别', alias: '类别' }],
                        style: {
                            title: { show: false, text: '' },
                            legend: { legend_pos: "top", orient: "horizontal" }
                        }
                    })
                })
            },
            
            // 右侧图例节点
            rightLegendNode: {
                id: 'right-legend-pie',
                code: 'PieChartWidget',
                widget: JSON.stringify({
                    beforeData: JSON.stringify({
                        xData: [{ label: '数值', alias: '数值', data: { code: 'value' }, func: { name: '求和' } }],
                        yData: [{ label: '类别', alias: '类别' }],
                        style: {
                            title: { show: true, text: '右侧图例测试' },
                            legend: { legend_pos: "right", orient: "vertical" }
                        }
                    })
                })
            },
            
            // 底部图例节点
            bottomLegendNode: {
                id: 'bottom-legend-pie',
                code: 'PieChartWidget',
                widget: JSON.stringify({
                    beforeData: JSON.stringify({
                        xData: [{ label: '数值', alias: '数值', data: { code: 'value' }, func: { name: '求和' } }],
                        yData: [{ label: '类别', alias: '类别' }],
                        style: {
                            title: { show: true, text: '底部图例测试' },
                            legend: { legend_pos: "bottom", orient: "horizontal" }
                        }
                    })
                })
            }
        }
    },
    
    mounted() {
        // 为所有测试组件加载数据
        setTimeout(() => {
            this.loadDataForAllComponents();
        }, 500);
    },
    
    methods: {
        loadDataForAllComponents() {
            const mockData = {
                status: 0,
                data: this.testData
            };
            
            const componentIds = ['with-title-pie', 'no-title-pie', 'right-legend-pie', 'bottom-legend-pie'];
            
            componentIds.forEach(id => {
                const component = this.findPieCardComponent(id);
                if (component) {
                    component.getMessage({
                        data: JSON.stringify(mockData)
                    });
                }
            });
        },
        
        findPieCardComponent(id) {
            const findComponent = (children) => {
                for (let child of children) {
                    if (child.$options.name === 'PieCard' && child.node && child.node.id === id) {
                        return child;
                    }
                    if (child.$children && child.$children.length > 0) {
                        const found = findComponent(child.$children);
                        if (found) return found;
                    }
                }
                return null;
            };
            return findComponent(this.$children);
        }
    }
}
</script>

<style scoped lang="less">
.layout-test {
    padding: 20px;
    
    h2 {
        color: #303133;
        margin-bottom: 30px;
        text-align: center;
        border-bottom: 2px solid #409eff;
        padding-bottom: 10px;
    }
    
    .test-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 30px;
        
        .test-item {
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            padding: 15px;
            background-color: #fff;
            
            h3 {
                color: #606266;
                margin-bottom: 15px;
                font-size: 14px;
                text-align: center;
                background-color: #f5f7fa;
                padding: 8px;
                border-radius: 4px;
            }
            
            .chart-container {
                width: 100%;
                height: 300px;
                position: relative;
                border: 1px solid #e4e7ed;
                border-radius: 4px;
            }
        }
    }
    
    .test-description {
        background-color: #f0f9ff;
        padding: 20px;
        border-radius: 8px;
        border-left: 4px solid #409eff;
        
        h3 {
            color: #409eff;
            margin-bottom: 15px;
        }
        
        ul {
            margin: 0;
            padding-left: 20px;
            
            li {
                color: #606266;
                margin-bottom: 8px;
                line-height: 1.5;
                
                &::marker {
                    color: #67c23a;
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .test-grid {
        grid-template-columns: 1fr;
    }
}
</style>
