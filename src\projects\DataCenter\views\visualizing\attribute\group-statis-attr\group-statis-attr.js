import {attrCommon} from "../attr-mixins/attr-common";
import dataList from "./data-list";
import styleList from "./style-list";
import seniorList from "./senior-list";

/**
 * 表格分组统计属性组件
 * 用于配置表格的分组统计功能，包括数据配置、样式设置和高级选项
 */
export default {
    name: "groupStatisAttr",
    mixins: [attrCommon],
    components: {
        dataList,
        styleList,
        seniorList
    },
    methods: {
        /**
         * 设置轴名称 - 用于分组字段配置
         * @param {Object} node - 节点对象
         * @param {String} type - 类型
         * @param {Array} lists - 列表数据
         */
        setAxisName(node, type, lists) {
            this.$refs.style_list.setGroupStatisAxisName(node, type, lists);
        }
    }
};