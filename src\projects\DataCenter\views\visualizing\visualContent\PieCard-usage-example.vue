<template>
    <div class="usage-example">
        <h2>PieCard 使用示例</h2>
        
        <!-- 示例1: 基本用法 -->
        <div class="example-section">
            <h3>示例1: 民族分布统计</h3>
            <div class="chart-container">
                <PieCard 
                    :node="ethnicNode" 
                    :id="'ethnic-pie-card'"
                    :isView="false"
                />
            </div>
        </div>
        
        <!-- 示例2: 带格式化的数据 -->
        <div class="example-section">
            <h3>示例2: 销售额分布（带数字格式化）</h3>
            <div class="chart-container">
                <PieCard 
                    :node="salesNode" 
                    :id="'sales-pie-card'"
                    :isView="false"
                />
            </div>
        </div>
        
        <!-- 控制按钮 -->
        <div class="control-buttons">
            <el-button @click="refreshData">刷新数据</el-button>
            <el-button @click="switchToTable">切换到表格模式</el-button>
            <el-button @click="switchToChart">切换到图表模式</el-button>
        </div>
    </div>
</template>

<script>
import PieCard from './PieCard.vue'

export default {
    name: 'PieCardUsageExample',
    components: {
        PieCard
    },
    data() {
        return {
            // 民族分布数据节点配置
            ethnicNode: {
                id: 'ethnic-pie-card',
                code: 'PieChartWidget',
                widget: JSON.stringify({
                    beforeData: JSON.stringify({
                        xData: [{
                            label: '人数',
                            alias: '人数',
                            data: {
                                code: 'population',
                                numberFormat: '#,##0'
                            },
                            func: { name: '求和' }
                        }],
                        yData: [{
                            label: '民族名称',
                            alias: '民族'
                        }],
                        style: {
                            title: { show: true, text: '民族分布统计' },
                            legend: {
                                color_type: "custom",
                                color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'],
                                label_show: true,
                                label_pos: "outside",
                                label_cont: "dimension,percent"
                            }
                        }
                    })
                })
            },
            
            // 销售额数据节点配置
            salesNode: {
                id: 'sales-pie-card',
                code: 'PieChartWidget',
                widget: JSON.stringify({
                    beforeData: JSON.stringify({
                        xData: [{
                            label: '销售额',
                            alias: '销售额(万元)',
                            data: {
                                code: 'sales_amount',
                                numberFormat: '#,##0.00'
                            },
                            func: { name: '求和' }
                        }],
                        yData: [{
                            label: '产品类别',
                            alias: '产品类别'
                        }],
                        style: {
                            title: { show: true, text: '产品销售分布' },
                            legend: {
                                color_type: "custom",
                                color: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'],
                                label_show: true,
                                label_pos: "outside",
                                label_cont: "dimension,percent"
                            }
                        }
                    })
                })
            }
        }
    },
    
    mounted() {
        // 加载示例数据
        this.loadEthnicData();
        this.loadSalesData();
    },
    
    methods: {
        /**
         * 加载民族分布数据
         */
        loadEthnicData() {
            setTimeout(() => {
                const ethnicData = {
                    status: 0,
                    data: {
                        columns: ["民族名称", "人数"],
                        rows: [
                            { "民族名称": "汉族", "人数": 92 },
                            { "民族名称": "蒙古族", "人数": 5 },
                            { "民族名称": "藏族", "人数": 3 },
                            { "民族名称": "满族", "人数": 2 },
                            { "民族名称": "回族", "人数": 1 }
                        ],
                        dimsCodes: ["民族名称"],
                        formatMapping: {}
                    }
                };
                
                this.sendDataToComponent('ethnic-pie-card', ethnicData);
            }, 500);
        },
        
        /**
         * 加载销售数据
         */
        loadSalesData() {
            setTimeout(() => {
                const salesData = {
                    status: 0,
                    data: {
                        columns: ["产品类别", "销售额"],
                        rows: [
                            { "产品类别": "电子产品", "销售额": 1250.50 },
                            { "产品类别": "服装鞋帽", "销售额": 980.30 },
                            { "产品类别": "家居用品", "销售额": 750.80 },
                            { "产品类别": "食品饮料", "销售额": 650.20 },
                            { "产品类别": "图书文具", "销售额": 420.10 }
                        ],
                        dimsCodes: ["产品类别"],
                        formatMapping: {}
                    }
                };
                
                this.sendDataToComponent('sales-pie-card', salesData);
            }, 800);
        },
        
        /**
         * 发送数据到指定组件
         */
        sendDataToComponent(componentId, data) {
            const component = this.findPieCardComponent(componentId);
            if (component) {
                component.getMessage({
                    data: JSON.stringify(data)
                });
            }
        },
        
        /**
         * 查找指定的 PieCard 组件
         */
        findPieCardComponent(id) {
            const findComponent = (children) => {
                for (let child of children) {
                    if (child.$options.name === 'PieCard' && child.node && child.node.id === id) {
                        return child;
                    }
                    if (child.$children && child.$children.length > 0) {
                        const found = findComponent(child.$children);
                        if (found) return found;
                    }
                }
                return null;
            };
            return findComponent(this.$children);
        },
        
        /**
         * 刷新数据
         */
        refreshData() {
            this.loadEthnicData();
            this.loadSalesData();
            this.$message.success('数据已刷新');
        },
        
        /**
         * 切换到表格模式
         */
        switchToTable() {
            this.switchAllComponentsMode('table');
        },
        
        /**
         * 切换到图表模式
         */
        switchToChart() {
            this.switchAllComponentsMode('chart');
        },
        
        /**
         * 切换所有组件的显示模式
         */
        switchAllComponentsMode(mode) {
            const ethnicComponent = this.findPieCardComponent('ethnic-pie-card');
            const salesComponent = this.findPieCardComponent('sales-pie-card');
            
            if (ethnicComponent) {
                ethnicComponent.switchDisplayMode(mode);
            }
            if (salesComponent) {
                salesComponent.switchDisplayMode(mode);
            }
            
            this.$message.success(`已切换到${mode === 'table' ? '表格' : '图表'}模式`);
        }
    }
}
</script>

<style scoped lang="less">
.usage-example {
    padding: 20px;
    
    h2 {
        color: #303133;
        margin-bottom: 30px;
        text-align: center;
        border-bottom: 2px solid #409eff;
        padding-bottom: 10px;
    }
    
    .example-section {
        margin-bottom: 40px;
        
        h3 {
            color: #606266;
            margin-bottom: 15px;
            font-size: 16px;
        }
        
        .chart-container {
            width: 100%;
            height: 350px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            position: relative;
            background-color: #fff;
        }
    }
    
    .control-buttons {
        text-align: center;
        padding: 20px;
        background-color: #f5f7fa;
        border-radius: 4px;
        
        .el-button {
            margin: 0 10px;
        }
    }
}
</style>
