/**
 * 分组统计表格属性配置样式
 * 参考饼图样式实现
 */
.groupStatisAttr {
    .group-config-section {
        margin-bottom: 15px;
        
        .section-title {
            font-size: 12px;
            color: #606266;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .config-item {
            margin-bottom: 10px;
            
            .item-label {
                font-size: 12px;
                color: #909399;
                margin-bottom: 5px;
            }
        }
    }
    
    .group-field-list {
        .field-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            margin-bottom: 8px;
            background-color: #f8f9fa;
            
            &:hover {
                border-color: #c0c4cc;
                background-color: #ecf5ff;
            }
            
            .field-name {
                font-size: 12px;
                color: #303133;
                flex: 1;
            }
            
            .field-actions {
                display: flex;
                gap: 5px;
                
                .el-button {
                    padding: 2px 6px;
                    font-size: 11px;
                }
            }
        }
    }
    
    .statistics-config {
        .stat-type-selector {
            .el-checkbox-group {
                display: flex;
                flex-direction: column;
                gap: 8px;
                
                .el-checkbox {
                    margin-right: 0;
                    
                    .el-checkbox__label {
                        font-size: 12px;
                        color: #606266;
                    }
                }
            }
        }
    }
}
