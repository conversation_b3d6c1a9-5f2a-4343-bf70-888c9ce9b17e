import {attrSenior} from "../../attr-mixins/attr-senior";
import relation from "../../relation/index";

/**
 * 分组统计表格高级配置组件
 * 参考饼图高级配置实现
 */
export default {
    name: "groupStatisSeniorList",
    mixins: [attrSenior],
    components: {relation},
    data() {
        return {
            // 数据限制配置
            maxRows: 1000,
            nullValueDisplay: '-',
            hideZeroValues: false,
            
            // 性能优化配置
            enableVirtualScroll: false,
            enableLazyLoad: false,
            
            // 导出配置
            defaultExportFormat: 'xlsx'
        };
    },
    methods: {
        /**
         * 配置变化处理
         */
        onConfigChange() {
            this.$emit('nodeDataRenew');
        },

        /**
         * 获取高级数据 - 系统要求的方法
         * @returns {Object}
         */
        getSeniorData() {
            const vm = this;
            return {
                ...vm.getRefreshData(),
                ...vm.getLinkageData(),
                dataLimit: {
                    maxRows: vm.maxRows,
                    nullValueDisplay: vm.nullValueDisplay,
                    hideZeroValues: vm.hideZeroValues
                },
                performance: {
                    virtualScroll: vm.enableVirtualScroll,
                    lazyLoad: vm.enableLazyLoad
                },
                export: {
                    defaultFormat: vm.defaultExportFormat
                }
            };
        },

        /**
         * 初始化数值 - 系统要求的方法
         */
        initValue() {
            const vm = this;
            vm.$nextTick(() => {
                vm.node.setData = true;
                if (!vm.node.widget) return;
                let {senior} = JSON.parse(JSON.parse(vm.node.widget).beforeData);
                if (senior) {
                    // 恢复数据限制配置
                    if (senior.dataLimit) {
                        vm.maxRows = senior.dataLimit.maxRows || 1000;
                        vm.nullValueDisplay = senior.dataLimit.nullValueDisplay || '-';
                        vm.hideZeroValues = senior.dataLimit.hideZeroValues || false;
                    }
                    
                    // 恢复性能配置
                    if (senior.performance) {
                        vm.enableVirtualScroll = senior.performance.virtualScroll || false;
                        vm.enableLazyLoad = senior.performance.lazyLoad || false;
                    }
                    
                    // 恢复导出配置
                    if (senior.export) {
                        vm.defaultExportFormat = senior.export.defaultFormat || 'xlsx';
                    }
                }
                vm.node.setData = false;
            });
        }
    }
}
