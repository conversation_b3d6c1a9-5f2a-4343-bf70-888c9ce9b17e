# PieCard 环形饼图表格功能限制

## 功能说明

根据需求，当 `vm.node.code === 'RingPieChartWidget'` 时，PieCard 组件不显示表格切换功能，仅保留饼图显示模式。

## 实现方案

### 1. 添加计算属性判断

```javascript
computed: {
    /**
     * 判断是否为环形饼图
     * @returns {boolean} 是否为环形饼图
     */
    isRingPieChart() {
        return this.node && this.node.code === 'RingPieChartWidget';
    }
}
```

### 2. 条件渲染切换按钮

```html
<!-- 切换按钮 -->
<div class="chart-toggle-buttons" v-if="!loading && !data_empty && !isRingPieChart">
    <el-button-group>
        <el-button>饼图</el-button>
        <el-button>表格</el-button>
    </el-button-group>
</div>
```

### 3. 条件渲染表格容器

```html
<!-- 表格显示 -->
<div v-show="displayMode === 'table' && !isRingPieChart" class="table-container">
    <!-- 表格内容 -->
</div>
```

### 4. 修改切换方法

```javascript
switchDisplayMode(mode) {
    // 如果是环形饼图，不允许切换到表格模式
    if (this.isRingPieChart && mode === 'table') {
        console.warn('RingPieChartWidget does not support table mode');
        return;
    }
    
    this.displayMode = mode;
    // ... 其他逻辑
}
```

### 5. 修改数据生成方法

```javascript
generateTableData() {
    // 如果是环形饼图，不生成表格数据
    if (this.isRingPieChart) {
        this.tableData = [];
        return;
    }
    
    // ... 普通饼图的表格数据生成逻辑
}
```

### 6. 修改数据更新逻辑

```javascript
// 在 getMessage 方法中
if (vm.displayMode === 'table' && !vm.isRingPieChart) {
    vm.generateTableData();
    // ... 滚动条检查逻辑
}
```

## 功能限制效果

### ✅ 普通饼图 (PieChartWidget)
- 显示切换按钮
- 支持图表/表格切换
- 表格功能完全可用
- 滚动条功能正常

### ❌ 环形饼图 (RingPieChartWidget)
- 不显示切换按钮
- 仅支持图表模式
- 表格功能完全禁用
- 不生成表格数据

## 代码变更总结

### 新增内容
- `isRingPieChart` 计算属性
- 环形饼图类型检查逻辑

### 修改内容
- 切换按钮显示条件：`v-if="!loading && !data_empty && !isRingPieChart"`
- 表格容器显示条件：`v-show="displayMode === 'table' && !isRingPieChart"`
- `switchDisplayMode` 方法：添加环形饼图检查
- `generateTableData` 方法：跳过环形饼图处理
- `getMessage` 方法：条件生成表格数据

### 保持不变
- 环形饼图的图表显示功能
- 普通饼图的所有功能
- 其他组件功能和样式

## 测试验证

创建了 `PieCard-ring-test.vue` 测试页面，包含以下测试场景：

1. **普通饼图**：验证切换按钮正常显示和功能
2. **环形饼图-多维度**：验证不显示切换按钮
3. **环形饼图-单维度**：验证不显示切换按钮
4. **环形饼图-各种配置**：验证在不同配置下都不显示切换按钮

## 兼容性保证

- ✅ 不影响现有的环形饼图显示功能
- ✅ 不影响普通饼图的表格功能
- ✅ 保持所有原有的样式和交互
- ✅ 向后兼容，不破坏现有代码

## 使用说明

无需额外配置，组件会自动根据 `node.code` 判断图表类型：

- `PieChartWidget`：显示切换按钮，支持表格模式
- `RingPieChartWidget`：不显示切换按钮，仅支持图表模式

这样确保了环形饼图专注于其可视化展示功能，而普通饼图保持完整的表格切换能力。
