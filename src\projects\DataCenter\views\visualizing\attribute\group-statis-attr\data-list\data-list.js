import {attrMixins} from "../../attr-mixins/attr-mixins"
import {globalBus} from "@/api/globalBus";

/**
 * 分组统计表格数据配置组件
 * 参考饼图数据配置实现
 */
export default {
    name: "groupStatisDataList",
    props: {
        node: Object,
        dir_dataSetId: String
    },
    mixins: [attrMixins],
    data() {
        let formList = {
            filterData: {
                label: '过滤器',
                type: 'selectGroup',
                multipleLimit: 1,
                placeholder: '拖动维度/度量字段到此处',
                prop: 'filter',
                show: () => true,
            }
        };
        return {
            axisList: {
                drilling: {
                    label: "钻取/维度",
                    type: 'drill_select',
                    list: [],
                    show: (axis) => axis.list.length
                },
                xAxis: {
                    label: '分组字段/维度',
                    type: 'select',
                    value: [],
                    prop: 'xAxis',
                    placeholder: '拖动维度字段到此处',
                    options: [],
                    multipleLimit: 5, // 支持多个分组字段
                    showLimit: true,
                    show: () => true,
                    rule: [
                        {required: true, message: '请添加分组字段', trigger: 'blur'}
                    ]
                },
                yAxis: {
                    label: '统计字段/度量',
                    type: 'select',
                    value: [],
                    prop: 'yAxis',
                    placeholder: '拖动度量字段到此处',
                    options: [],
                    multipleLimit: 10, // 支持多个统计字段
                    showLimit: true,
                    show: () => true,
                    rule: [
                        {required: false, message: '请添加统计字段', trigger: 'blur'}
                    ]
                }
            },
            formList,
            
            // 分组统计特有配置
            groupFields: [],
            statisticsTypes: ['count'],
            statisticsFields: [],
            sortField: '',
            sortOrder: 'asc',
            availableFields: [],
            numericFields: []
        }
    },
    computed: {
        /**
         * 是否需要选择统计字段
         * @returns {Boolean}
         */
        needStatisticsFields() {
            return this.statisticsTypes.some(type => ['sum', 'avg', 'max', 'min'].includes(type));
        },

        /**
         * 所有可排序字段
         * @returns {Array}
         */
        allSortableFields() {
            let fields = [...this.availableFields];
            
            // 添加统计字段
            this.statisticsTypes.forEach(type => {
                if (type === 'count') {
                    fields.push({
                        label: '计数',
                        value: 'count_value'
                    });
                } else if (this.needStatisticsFields) {
                    this.statisticsFields.forEach(field => {
                        const fieldInfo = this.numericFields.find(f => f.value === field);
                        if (fieldInfo) {
                            fields.push({
                                label: `${fieldInfo.label}(${this.getStatTypeLabel(type)})`,
                                value: `${field}_${type}`
                            });
                        }
                    });
                }
            });
            
            return fields;
        },

        /**
         * 图表数据 - 系统要求的属性
         * @returns {Object}
         */
        chartData() {
            return {
                groupFields: this.groupFields,
                statisticsTypes: this.statisticsTypes,
                statisticsFields: this.statisticsFields,
                sortField: this.sortField,
                sortOrder: this.sortOrder,
                axisList: this.axisList,
                filterSelects: this.filterSelects
            };
        }
    },
    watch: {
        /**
         * 监听轴列表变化，更新可用字段
         */
        axisList: {
            handler(newVal) {
                this.updateAvailableFields();
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        /**
         * 更新可用字段列表
         */
        updateAvailableFields() {
            this.availableFields = [];
            this.numericFields = [];
            
            if (this.axisList && this.axisList.xAxis && this.axisList.xAxis.value) {
                this.axisList.xAxis.value.forEach(node => {
                    const field = {
                        label: node.alias || node.name,
                        value: node.name,
                        type: node.type
                    };
                    this.availableFields.push(field);
                });
            }
            
            if (this.axisList && this.axisList.yAxis && this.axisList.yAxis.value) {
                this.axisList.yAxis.value.forEach(node => {
                    const field = {
                        label: node.alias || node.name,
                        value: node.name,
                        type: node.type
                    };
                    this.availableFields.push(field);
                    
                    // 数值类型字段可用于统计
                    if (['number', 'decimal', 'int', 'float', 'double'].includes(node.type)) {
                        this.numericFields.push(field);
                    }
                });
            }
        },

        /**
         * 分组字段变化处理
         */
        onGroupFieldsChange() {
            this.$emit('nodeDataRenew');
        },

        /**
         * 统计类型变化处理
         */
        onStatisticsTypesChange() {
            // 如果不需要统计字段，清空统计字段选择
            if (!this.needStatisticsFields) {
                this.statisticsFields = [];
            }
            this.$emit('nodeDataRenew');
        },

        /**
         * 统计字段变化处理
         */
        onStatisticsFieldsChange() {
            this.$emit('nodeDataRenew');
        },

        /**
         * 获取统计类型标签
         * @param {String} type - 统计类型
         * @returns {String}
         */
        getStatTypeLabel(type) {
            const labels = {
                'count': '计数',
                'sum': '求和',
                'avg': '平均值',
                'max': '最大值',
                'min': '最小值'
            };
            return labels[type] || type;
        },

        /**
         * 初始化数值 - 系统要求的方法
         */
        initValue() {
            this.updateAvailableFields();
        }
    }
}
