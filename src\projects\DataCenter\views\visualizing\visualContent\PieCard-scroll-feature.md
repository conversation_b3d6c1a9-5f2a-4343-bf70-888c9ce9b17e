# PieCard 表格滚动条功能

## 功能概述

为 PieCard 组件的表格模式添加了智能滚动条功能，当表格内容超出容器高度时自动显示滚动条，提升用户体验。

## 实现细节

### 1. 表格容器优化

```html
<div 
    v-show="displayMode === 'table'" 
    class="table-container" 
    :style="{
        'height': tableContainerHeight, 
        'max-height': height, 
        'overflow': 'auto'
    }"
>
    <!-- 表格内容 -->
</div>
```

### 2. 动态高度计算

```javascript
computed: {
    /**
     * 计算表格最大高度
     */
    tableMaxHeight() {
        let baseHeight = '100%';
        let deductHeight = 50; // 基础高度减去各种元素占用的空间
        
        if (this.showDrill) {
            deductHeight += 30; // 钻取提示的高度
        }
        
        return `calc(${baseHeight} - ${deductHeight}px)`;
    },
    
    /**
     * 计算表格容器的实际高度
     */
    tableContainerHeight() {
        if (this.showDrill) {
            return 'calc(100% - 30px)';
        }
        return '100%';
    }
}
```

### 3. 滚动条检测

```javascript
/**
 * 检查表格是否需要滚动条
 */
checkTableNeedsScroll() {
    this.$nextTick(() => {
        const tableContainer = this.$el.querySelector('.table-container');
        const table = this.$el.querySelector('.pie-statistics-table .el-table');
        
        if (tableContainer && table) {
            const containerHeight = tableContainer.clientHeight;
            const tableHeight = table.scrollHeight;
            
            // 如果表格内容高度超过容器高度，添加滚动提示类
            if (tableHeight > containerHeight) {
                tableContainer.classList.add('has-scroll');
            } else {
                tableContainer.classList.remove('has-scroll');
            }
        }
    });
}
```

### 4. 自定义滚动条样式

```css
/* 自定义滚动条样式 */
&::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

&::-webkit-scrollbar-thumb {
    background-color: #c0c4cc;
    border-radius: 4px;
    
    &:hover {
        background-color: #909399;
    }
}

&::-webkit-scrollbar-track {
    background-color: #f5f7fa;
    border-radius: 4px;
}
```

### 5. 滚动提示效果

```css
/* 滚动提示样式 */
&.has-scroll {
    &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 20px;
        background: linear-gradient(to bottom, rgba(255,255,255,0), rgba(245,247,250,0.8));
        pointer-events: none;
        z-index: 1;
    }
}
```

### 6. 表头固定

```css
/* 表头固定样式 */
.el-table__header-wrapper {
    position: sticky;
    top: 0;
    z-index: 2;
}
```

## 触发时机

滚动条检测在以下情况下触发：

1. **切换到表格模式时**
```javascript
switchDisplayMode(mode) {
    this.displayMode = mode;
    if (mode === 'table') {
        this.generateTableData();
        
        // 在表格渲染完成后检查是否需要滚动条
        this.$nextTick(() => {
            this.checkTableNeedsScroll();
        });
    }
}
```

2. **数据更新时**
```javascript
if (vm.displayMode === 'table') {
    vm.generateTableData();
    
    // 检查表格是否需要滚动条
    vm.$nextTick(() => {
        vm.checkTableNeedsScroll();
    });
}
```

## 响应式适配

针对不同屏幕尺寸进行了优化：

```css
@media (max-width: 768px) {
    .table-container {
        padding: 35px 5px 5px 5px;
        
        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        
        /deep/ .el-table {
            font-size: 11px;
            
            .el-table__header th {
                padding: 6px 0;
            }
            
            .el-table__body td {
                padding: 6px 0;
            }
        }
    }
}
```

## 兼容性保证

- ✅ 普通饼图和嵌套饼图都支持
- ✅ 不同数据量级都能正常工作
- ✅ 固定高度和自适应高度容器都支持
- ✅ 移动端和桌面端都有良好体验
- ✅ 不影响原有功能和样式

## 测试验证

创建了 `PieCard-scroll-test.vue` 测试页面，包含以下测试场景：

1. **少量数据**：验证无需滚动条的情况
2. **大量数据**：验证需要滚动条的情况
3. **嵌套饼图大量数据**：验证复杂数据结构的滚动效果
4. **固定高度容器**：验证在限制高度下的滚动效果

## 使用说明

无需额外配置，组件会自动检测表格内容高度并在需要时显示滚动条。表头会固定在顶部，方便用户在滚动时查看列名。
