<template>
    <div class="visualPanel" :class="{'ce-full-screen' : isFullScreen}" ref="parent" @dragover="allowDrop($event)"
         @drop="addNode($event)">
        <div class="visualCont" ref="visual" v-if="load">
            <grid-layout
                    :layout.sync="node_list_show"
                    :col-num="colNum"
                    :row-height="rowNum"
                    :is-draggable="!isFullScreen"
                    :is-resizable="!isFullScreen"
                    :is-mirrored="mirrored"
                    :vertical-compact="verticalCompact"
                    :margin="gridMargin"
                    :use-css-transforms="useCssTransforms"
                    :prevent-collision="preventCollision"
                    :responsive="responsive"
                    @layout-created="layoutCreatedEvent"
                    @layout-before-mount="layoutBeforeMountEvent"
                    @layout-mounted="layoutMountedEvent"
                    @layout-ready="layoutReadyEvent"
                    @layout-updated="layoutUpdatedEvent"
                    @breakpoint-changed="breakpointChangedEvent"
            >
                <grid-item v-for="node in node_list_show"
                           :x="node.x"
                           :y="node.y"
                           :w="node.w"
                           :h="node.h"
                           :i="node.i"
                           :minW="node.minW"
                           :minH="node.minH"
                           :key="node.i"
                           :dragAllowFrom="dragCss"
                           @resize="resizeEvent"
                           @move="moveEvent"
                           @resized="resizedEvent"
                           @moved="movedEvent"
                >
                    <VisualNode :isView="isView" :isPreview="isFullScreen"
                                :class="{'visualNode_hover' : !isFullScreen }"
                                :label="node.label" :node="node"
                                :id="node.i"
                                @exportExcel="exportExcel"
                                @getSql="getSql"
                                @copyPanel="copyPanel"
                                @deleteNode="deleteNode"
                                @editNode="editNode">
                        <template>
                            <HistogramCard @setLinkage="setLinkage" :isView="isView" :ref="'chart'+node.id" :node="node"
                                           :id="node.id"
                                           :moving="moving"
                                           v-if="node.code === 'BarChartWidget'|| node.code === 'StackBarChartWidget'|| node.code === 'TransverseBarChartWidget'|| node.code === 'TransverseStackBarChartWidget'"/>
                            <LineCard @setLinkage="setLinkage" :isView="isView" :ref="'chart'+node.id" :node="node"
                                      :id="node.id"
                                      :moving="moving"
                                      v-if="node.code === 'LineChartWidget' || node.code === 'AreaGraphWidget'"/>
                            <SearchCard :isView="isView" :ref="'chart'+node.id" :node="node" :nodeList="nodeList"
                                        :id="node.id"
                                        :moving="moving"
                                        v-if="node.code === 'SelectWidget'"
                                        @getFormList="getFormList"
                                        @filterAssociationCharts="filterAssociationCharts"/>
                            <TextCard :ref="'chart'+node.id" :node="node" :id="node.id"
                                      :moving="moving"
                                      v-if="node.code === 'TextWidget'"/>
                            <PieCard @setLinkage="setLinkage" :isView="isView" :ref="'chart'+node.id" :node="node"
                                     :id="node.id"
                                     :moving="moving"
                                     v-if="node.code === 'PieChartWidget' || node.code === 'RingPieChartWidget'"/>
                            <TableCard @setLinkage="setLinkage" :isView="isView" :ref="'chart'+node.id" :node="node"
                                       :id="node.id"
                                       :moving="moving"
                                       v-if="node.code === 'TableChartWidget'"/>
                            <MapCard @setLinkage="setLinkage" :isView="isView" :ref="'chart'+node.id" :node="node"
                                     :id="node.id"
                                     :moving="moving"
                                     v-if="node.code === 'MapChartWidget' ||  node.code === 'ColourMap'"/>
                            <heatMapCard @setLinkage="setLinkage"
                                         :isView="isView"
                                         :moving="moving"
                                         :ref="'chart'+node.id"
                                         :node="node"
                                         :id="node.id"
                                         v-if="node.code === 'HeatMap'"
                            >
                            </heatMapCard>
                            <wordCloud @setLinkage="setLinkage"
                                       :isView="isView"
                                       :moving="moving"
                                       :ref="'chart'+node.id"
                                       :node="node"
                                       :id="node.id"
                                       v-if="node.code === 'WordCloudWidget'"
                            >
                            </wordCloud>
                            <combination @setLinkage="setLinkage"
                                         :isView="isView"
                                         :moving="moving"
                                         :ref="'chart'+node.id"
                                         :node="node"
                                         :id="node.id"
                                         v-if="node.code === 'CombinationWidget'"
                            >
                            </combination>
                            <indicator
                                    :isView="isView"
                                    :moving="moving"
                                    :ref="'chart'+node.id"
                                    :node="node"
                                    :id="node.id"
                                    v-if="node.code === 'IndicatorCardWidget'"
                            ></indicator>
                            <pgisCard
                                    :isView="isView"
                                    :moving="moving"
                                    :ref="'chart'+node.id"
                                    :node="node"
                                    :id="node.id"
                                    v-if="node.code === 'PGISWidget'"
                                    @setLinkage="setLinkage"
                            ></pgisCard>
                            <tab-card
                                    :isView="isView"
                                    :moving="moving"
                                    :ref="'chart'+node.id"
                                    :node="node"
                                    :id="node.id"
                                    :canDrop="canDrop"
                                    :plugNode="plugNode"
                                    :nodeList="nodeList"
                                    :isFullScreen="isFullScreen"
                                    :colNum="colNum"
                                    :rowNum="rowNum"
                                    :mirrored="mirrored"
                                    :verticalCompact="verticalCompact"
                                    :gridMargin="gridMargin"
                                    :useCssTransforms="useCssTransforms"
                                    :preventCollision="preventCollision"
                                    :responsive="responsive"
                                    :dragCss="dragCss"
                                    v-if="node.code === 'TabWidget'"
                                    @addTabChart="addTabChart"
                                    @editNode="editNode"
                                    @deleteTabListNode="deleteTabListNode"
                                    @deleteOperate="deleteTabChart"
                                    @getSql="getSql"
                                    @setLinkage="setLinkage"
                                    @moveEvent="moveEvent"
                                    @resizeEvent="resizeEvent"
                                    @movedEvent="movedEvent"
                                    @resizedEvent="resizedEvent"
                                    @getFormList="getFormList"
                                    @filterAssociationCharts="filterAssociationCharts"
                                    @copyPanel="copyPanel"
                            >
                            </tab-card>
                            <bubble-map
                                    @setLinkage="setLinkage"
                                    :isView="isView"
                                    :moving="moving"
                                    :ref="'chart'+node.id"
                                    :node="node"
                                    :id="node.id"
                                    v-if="node.code === 'BubbleMap'"
                            ></bubble-map>
                            <Radar
                                    @setLinkage="setLinkage"
                                    :isView="isView"
                                    :moving="moving"
                                    :ref="'chart'+node.id"
                                    :node="node"
                                    :id="node.id"
                                    v-if="node.code === 'RadarWidget'"
                            ></Radar>
                            <Graph
                                    @setLinkage="setLinkage"
                                    :isView="isView"
                                    :moving="moving"
                                    :ref="'chart'+node.id"
                                    :node="node"
                                    :id="node.id"
                                    v-if="node.code === 'RelationshipWidget'"
                            ></Graph>
                            <BusinessRelation
                                    @setLinkage="setLinkage"
                                    :isView="isView"
                                    :moving="moving"
                                    :ref="'chart'+node.id"
                                    :node="node"
                                    :id="node.id"
                                    v-if="node.code === 'BusinessRelationshipWidget'"
                            ></BusinessRelation>
                            <Form
                                    @setLinkage="setLinkage"
                                    :isView="isView"
                                    :moving="moving"
                                    :ref="'chart'+node.id"
                                    :node="node"
                                    :id="node.id"
                                    v-if="node.code === 'FormWidget'"
                            ></Form>
                            <GroupStatisCard
                                    @setLinkage="setLinkage"
                                    :isView="isView"
                                    :moving="moving"
                                    :ref="'chart'+node.id"
                                    :node="node"
                                    :id="node.id"
                                    v-if="node.code === 'GroupStatisWidget'"
                            ></GroupStatisCard>
                            </template>
                    </VisualNode>
                </grid-item>
            </grid-layout>
        </div>
        <sql-page ref="sql"></sql-page>
    </div>
</template>

<script>
    import VisualNode from './VisualNode'
    import vueDraggableResizable from '@/components/vuedragableresize/VueDraggableResizable'
    // 导入默认样式
    import '@/components/vuedragableresize/vue-draggable-resizable.css'
    import {globalBus} from "@/api/globalBus"
    import {common} from "@/api/commonMethods/common";
    import VueGridLayout from 'vue-grid-layout';
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {servicesMixins} from "../service-mixins/service-mixins";
    import sqlPage from "./sql-page/index"
    import * as $ from "jquery";
    import {
        BubbleMap,
        combination,
        heatMapCard,
        HistogramCard,
        indicator,
        LineCard,
        MapCard,
        pgisCard,
        PieCard,
        Radar,
        SearchCard,
        tabCard,
        TableCard,
        TextCard,
        wordCloud,
        Graph,
        Form,
        BusinessRelation,
        GroupStatisCard,
    } from "../visualContent/chart-mixins/charts-index"

    export default {
        name: "VisualPanel",
        props: {
            isView: Boolean
        },
        mixins: [common, commonMixins, servicesMixins],
        components: {
            VisualNode,
            vueDraggableResizable,
            HistogramCard,
            LineCard,
            SearchCard,
            TextCard,
            PieCard,
            TableCard,
            MapCard,
            GridLayout: VueGridLayout.GridLayout,
            GridItem: VueGridLayout.GridItem,
            indicator,
            sqlPage,
            tabCard,
            heatMapCard,
            wordCloud,
            pgisCard,
            BubbleMap,
            combination,
            Radar,
            Graph,
            Form,
            BusinessRelation,
            GroupStatisCard,
        },
        computed: {
            //显示的图表
            node_list_show: {
                get() {
                    return this.nodeList.filter(node => node.show);
                },
                set(v) {
                    this.nodeList = this.nodeList.filter(node => !node.show).concat(v);
                }
            },
            responsive(){
                return !this.isView;
            },
        },
        data() {
            return {
                useCssTransforms: true,
                verticalCompact: true,
                mirrored: false,
                preventCollision: false,
                dragCss: '.ce-header_title',
                colNum: 12,
                rowNum: 40,
                gridMargin: [10, 10],
                nodeList: [],
                currentWidth: 0,
                currentHeight: 0,
                currentLeft: 0,
                currentTop: 0,
                currentId: '',
                plugNode: {},
                index: 0,
                parentLimit: true,
                isFullScreen: false,
                isSnap: true,
                tolerance: 20,
                width: 4,
                height: 5,
                load: true,
                parentBoundary: [1, 4],//拖拽范围，父级宽高的倍数
                canDrop: false,
                moving: false,
                screenY: null,
                isMove: false,
                windowObj: {},
                limitSize: 0,
            }
        },
        methods: {
            deleteTabChart(node) {
                this.deleteOperate(node.id);
                this.$emit('closeAttr', '', node);
            },
            addTabChart(e, tabId, tab) {
                this.addNode(e, true, tabId, tab.value, tab);
            },
            getFormList(formList, id) {
                const vm = this;
                vm.nodeList.forEach(item => {
                    let widget = JSON.parse(item.widget), itemData = JSON.parse(widget.beforeData);
                    if (item.id === id) {
                        itemData.filterList = JSON.parse(JSON.stringify(formList));
                        widget.beforeData = JSON.stringify(itemData);
                        item.widget = JSON.stringify(widget);
                    }
                })
            },
            relationAllChartsPreview(item, tabId) {
                const vm = this;
                if (vm.$refs['chart' + item.id] && vm.$refs['chart' + item.id].length > 0) {
                    vm.$refs['chart' + item.id][0].preview();
                } else if (vm.$refs['chart' + tabId] &&
                    vm.$refs['chart' + tabId].length > 0 &&
                    vm.$refs['chart' + tabId][0].$refs['chart' + tabId + item.id] &&
                    vm.$refs['chart' + tabId][0].$refs['chart' + tabId + item.id].length > 0) {
                    vm.$nextTick(() => {
                        vm.$refs['chart' + tabId][0].$refs['chart' + tabId + item.id][0].preview();
                    })

                }
            },
            linkagePreview(vm, item, filter) {
                let widget = JSON.parse(item.widget), beforeData = JSON.parse(widget.beforeData),
                    tabId = beforeData.tabId, tabVal = beforeData.tabVal;
                widget.linkageFilter = JSON.stringify(filter);
                item.widget = JSON.stringify(widget);
                vm.relationAllChartsPreview(item, tabId);
            },
            /**
             * 联动拼后端接收的数据，
             * formatMapping 时间维度转换过的格式映射
             * */
            setLinkage(linkage, event, datasetId, nodeData, node, dims, dimsCodes , formatMapping) {
                const vm = this, {nodeList} = this;
                linkage.fields.forEach((link, i) => {
                    nodeList.forEach(item => {
                        let filter = [];
                        let link_data = linkage.relationsData[i];
                        let field_d = nodeData.yData.filter(f => link.indexOf(f.data.code) > -1);
                        let {beforeData} = JSON.parse(item.widget), itemData = JSON.parse(beforeData);
                        if (link_data.same_val.length && link_data.same_val.split(",").indexOf(item.linkageId) > -1 && link_data.enough && datasetId === itemData.classifierStatId) {
                            field_d.forEach((fie, i) => {
                                if (node.code !== "RingPieChartWidget") {
                                    let inx;
                                    nodeData.yData.forEach((d, index) => {
                                        if (d.data.label === fie.label) {
                                            inx = index;
                                        }
                                    });
                                    let fieldD;
                                    if (dims && dimsCodes.indexOf(fie.label) === -1) {
                                        fieldD = {
                                            code: dims.filedCode,
                                            id: dims.filedId,
                                            label: dims.data.label,
                                            alias: dims.data.fieldAlias || "",
                                            indexType: dims.data.indexType,
                                            exp: dims.data.exp,
                                            format: dims.data.format
                                        };
                                    } else {
                                        fieldD = fie.data;
                                    }

                                    let name;
                                    if(node.code === "TableChartWidget"){
                                        name = event[fieldD.code];
                                    }else {
                                        name = inx === 0 ? event.name : event.seriesName;
                                    }
                                    formatMapping && formatMapping[name] ? name =  formatMapping[name] : true;
                                    filter.push({
                                        value: name,
                                        type: "precise",
                                        parentType: "text",
                                        field: fieldD
                                    })

                                } else {
                                    if (event.seriesName === fie.label) {
                                        let name = formatMapping && formatMapping[event.name] ? formatMapping[event.name] : event.name;
                                        filter.push({
                                            value: name,
                                            type: "precise",
                                            parentType: "text",
                                            field: fie.data
                                        })
                                    }
                                }
                            });
                            if (filter.length) {
                                vm.linkagePreview(vm, item, filter);
                            }
                        } else if (link_data.dif_val.length && link_data.dif_val.indexOf(item.linkageId) > -1 && link_data.enough && itemData.yData.length) {
                            let dif_data = link_data.dif_dataset_charts.filter(dif => dif.id === item.linkageId)[0],
                                dimension = dif_data.dimension.filter(yd => yd.value === dif_data.field),
                                dif_field;
                            if (dimension.length) {
                                dif_field = dimension[0];
                                let name;
                                if(node.code === "TableChartWidget"){
                                    let tableField = field_d.find(item => item.data.code === dif_field.code);
                                    if(tableField){
                                        name = event[tableField.data.code];
                                    }else {
                                        name = event[field_d[0].data.code];
                                    }
                                }else {
                                    name = formatMapping && formatMapping[event.name] ? formatMapping[event.name] : event.name;
                                }
                                filter.push({
                                    value: name,
                                    type: "precise",
                                    parentType: "text",
                                    field: dif_field
                                });
                                vm.linkagePreview(vm, item, filter);
                            }
                        }
                    })
                })
            },
            addFastTitle(DatasetMeasures){
                let hasFastCount = false;
                for(let mea of DatasetMeasures){
                    let funcJ = JSON.parse( mea.functionsJson);
                    if(funcJ && funcJ.length && funcJ[0]){
                        hasFastCount = funcJ.length > 0;
                    }
                }
                return hasFastCount ? '(快速计算)' : '';
            },
            getSql(id, node) {
                const vm = this, {visualServices, visualMock, isView} = this;
                let services = vm.getServices(visualServices, visualMock);
                let widget = JSON.parse(node.widget), nodeData = JSON.parse(widget.beforeData);
                let fast_title = vm.addFastTitle(widget.widgetDataset.widgetDatasetMeasures);
                if (node.code === "MapChartWidget" || node.code === "ColourMap" || node.code === "BubbleMap") {
                    widget.mapCode = nodeData.style && nodeData.style.funcSettings.mapCode && nodeData.style.funcSettings.mapCode.length ? nodeData.style.funcSettings.mapCode.slice(-1)[0] : 'china';
                }
                if (nodeData.filterList && nodeData.filterList.length > 0) {
                    let dims = null;
                    let query = nodeData.filterList.filter(list => {
                        if (list.dim) dims = list.dim;
                        return list.filter && list.filter.addVal !== "";
                    });
                    widget.query = JSON.stringify(query);
                    let dimsIndex = widget.widgetDataset.widgetDatasetDims.length;
                    if (dims) {
                        dims.index = dimsIndex;
                        widget.widgetDataset.widgetDatasetDims.push(dims);
                    }
                }
                services.getSql({
                    code: node.code,
                    data: JSON.stringify(widget),
                    type: isView
                }).then(res => {
                    if (res.data.status === 0) {
                        let result = res.data.data;
                        let title = `SQL > ${widget.title}${fast_title}`;
                        // vm.$refs.sql.show(result, title);
                        vm.showSqlPage(result , title);
                    }
                })
            },
            showSqlPage(data,title){
                const vm = this;
                let layer = vm.$dgLayer({
                    title ,
                    content: require("@/components/common/sqlPage/index.vue"),
                    props: {
                        value : data,
                    },
                    area: ["1024px", "80%"],
                    move : false,
                })
            },
            copyPanel(id, node , tab ) {
                const vm = this, {visualServices, visualMock} = this;
                let services = vm.getServices(visualServices, visualMock);
                vm.confirm("复制", "确认复制该面板?", () => {
                    services.initWidget().then(res => {
                        if (res.data.status === 0) {
                            let nodeId = res.data.data, new_node = JSON.parse(JSON.stringify(node));
                            let widget = JSON.parse(new_node.widget) , beforeData = JSON.parse(widget.beforeData),
                                    tabId = beforeData.tabId ,tabVal = beforeData.tabVal;
                            new_node.id = nodeId;
                            new_node.i = nodeId;
                            new_node.linkageId = nodeId;
                            widget.id = nodeId;
                            let {x, y} = this.auto_position(new_node.w , tab);
                            new_node.x = x;
                            new_node.left = x;
                            new_node.y = y;
                            new_node.top = y;
                            widget.x = x;
                            widget.y = y;
                            new_node.widget = JSON.stringify(widget);
                            vm.nodeList.push(new_node);
                            vm.showAttrPanel(new_node);
                            vm.$message.success("复制成功");
                            vm.$nextTick(() => {
                                if(tabId) vm.$refs['chart' + tabId][0].setTabPanelNode(new_node, tabVal);
                                vm.emitEditVisual(new_node);
                                vm.locationPanel(nodeId)
                            })
                        }
                    })
                })
            },
            locationPanel(nodeId) {
                const vm = this;
                if (!vm.$refs['chart' + nodeId]) return;
                let node_dom = vm.$refs['chart' + nodeId][0].$el.parentNode.parentNode.parentNode,
                    pos = node_dom.style.transform.match(/\(([^)]*)\)/)[1].split(',');
                let x = parseInt(pos[0]) - 10, y = parseInt(pos[1]) - 10;
                setTimeout(() => {
                    if (vm.$refs.visual.scroll) {
                        vm.$refs.visual.scroll({top: y, behavior: "smooth"});
                        vm.$refs.parent.scrollTo({left: x, behavior: "smooth"});
                    } else {
                        $(vm.$refs.visual).animate({'scrollTop': y}, 100);
                        $(vm.$refs.parent).animate({'scrollLeft': x}, 100);
                    }

                }, 500);
            },
            layoutCreatedEvent(newLayout) {
                // console.log("Created layout: ", newLayout)
            },
            layoutBeforeMountEvent(newLayout) {
                // console.log("beforeMount layout: ", newLayout)
            },
            layoutMountedEvent(newLayout) {
                // console.log("beforeMount layout: ", newLayout)
            },
            layoutReadyEvent(newLayout) {
                // console.log("Ready layout: ", newLayout)
            },
            layoutUpdatedEvent(newLayout) {
                const vm = this;
                vm.isMove = false;
                /*newLayout.forEach(item => {
                    // item.left = item.x;
                    // item.top = item.y;
                })*/
            },
            breakpointChangedEvent(newBreakpoint, newLayout) {
                // console.log("breakpoint changed breakpoint=", newBreakpoint, ", layout: ", newLayout );
            },
            moveEvent(i, newX, newY) {
                let vm = this;
                vm.nodeList.forEach(node => {
                    if (node.id === i) {
                        node.top = newY;
                        node.left = newX;
                        node.y = newY;
                        node.x = newX;
                    }
                });
                // vm.moveScroll();
            },
            resizeEvent(i, newH, newW, newHPx, newWPx) {
                let vm = this, y;
                vm.nodeList.forEach(node => {
                    if (node.id === i) {
                        node.width = newW;
                        node.height = newH;
                        node.w = newW;
                        node.h = newH;
                    }
                });
                vm.$nextTick(() => {
                    vm.resizeScroll();
                    if (!vm.$refs['chart' + i]) return;
                    vm.$refs['chart' + i][0].chartResize();
                });
            },
            gridScroll(scrollTop, direction) {
                const vm = this;
                let visualH = vm.$refs.visual.children[0].offsetHeight;
                let top = scrollTop;
                if (direction) {
                    top = scrollTop - 10;
                } else {
                    top = scrollTop + 10;
                }
                vm.$refs.visual.scroll({top: top, behavior: "smooth"});
                if (vm.isMove && top > 10 && top < visualH) {
                    setTimeout(() => {
                        vm.gridScroll(top, direction);
                    }, 100);
                } else {
                    vm.isMove = false;
                }
            },
            moveScroll() {
                const vm = this;
                let scrollTop = vm.$refs.visual.scrollTop;
                vm.windowObj = window;
                if (!vm.windowObj.event) return;
                if (!vm.screenY) {
                    vm.screenY = vm.windowObj.event.screenY;
                } else {
                    let direction = vm.screenY >= vm.windowObj.event.screenY;
                    let num = vm.screenY > vm.windowObj.event.screenY ? 10 : vm.screenY < vm.windowObj.event.screenY ? -10 : 0;
                    if (!vm.isMove) {
                        vm.isMove = true;
                        vm.gridScroll(scrollTop, direction);
                    }
                    vm.screenY = vm.windowObj.event.screenY + num;
                }
            },
            gridScrollTop(gridHeight, visualH) {
                const vm = this;
                let top = gridHeight - visualH + 20;
                if (vm.$refs.visual.scroll) {
                    vm.$refs.visual.scroll({top: top, behavior: "smooth"});
                } else {
                    $(vm.$refs.visual).animate({'scrollTop': top}, 100);
                }
            },
            resizeScroll() {
                const vm = this;
                let gridHeight = vm.$refs.visual.children[0].offsetHeight;
                let visualH = vm.$refs.visual.offsetHeight, offsetY = window.event.clientY - 125,
                    scrollTop = vm.$refs.visual.scrollTop;
                if (offsetY >= visualH) {
                    vm.gridScrollTop(gridHeight, visualH)
                }

            },
            movedEvent(i, newX, newY) {
                let vm = this;
                vm.nodeList.forEach(node => {
                    if (node.id === i) {
                        node.top = newY;
                        node.left = newX;
                        node.y = newY;
                        node.x = newX;
                    }
                });
                vm.isMove = false;
            },
            resizedEvent(i, newH, newW, newHPx, newWPx) {
                let vm = this;
                vm.nodeList.forEach(node => {
                    if (node.id === i) {
                        node.width = newW;
                        node.height = newH;
                        node.w = newW;
                        node.h = newH;
                    }
                });
                vm.$nextTick(() => {
                    if (!vm.$refs['chart' + i]) return;
                    vm.$refs['chart' + i][0].chartResize();
                });
            },
            zIndexChange(node) {
                this.nodeList.forEach((n, i) => {
                    if (n.widget.beforeData) {
                        let bData = JSON.parse(n.widget.beforeData);
                        if (n.id === node.id) {
                            n.zIndex = 1000;
                            bData.zIndex = n.zIndex;
                            n.widget.beforeData = JSON.stringify(bData);
                        } else {
                            n.zIndex > 10 ?
                                n.zIndex-- :
                                n.zIndex = 10;
                            bData.zIndex = n.zIndex;
                            n.widget.beforeData = JSON.stringify(bData);
                        }
                    }
                });
            },
            /**
             *搜索 过滤图表数据
             * @param list 过滤条件
             * @param relation_charts 关联图表
             * 需要清除 联动过滤条件 linkageFilter
             */
            filterAssociationCharts(list, relation_charts) { //关联图表添加过滤条件
                const vm = this;
                this.nodeList.forEach(node => {
                    let widget = JSON.parse(node.widget), nodeData = JSON.parse(widget.beforeData),
                        tabId = nodeData.tabId;
                    if (relation_charts.indexOf(node.linkageId) > -1 && node.code !== 'SelectWidget') {
                        nodeData.filterList = vm.getSameDatasetList(list, nodeData.datasetId);
                        delete widget.linkageFilter;
                        widget.beforeData = JSON.stringify(nodeData);
                        node.widget = JSON.stringify(widget);
                        vm.relationAllChartsPreview(node, tabId);
                    }
                });
            },
            /**
             * 筛选同数据集的过滤条件
             * @param list
             * @param datasetId
             */
            getSameDatasetList(list, datasetId) {
                return list.filter(li => li.dataset === datasetId);
            },
            reLoad() {
                // this.load = false;
                const $this = this;
                setTimeout(() => {
                    $this.load = true;
                }, 200);
            },
            setCanDrop() {
                globalBus.$on('dragend', this.isAllow);
            },
            isAllow() {
                this.canDrop = false;
            },
            renewNode() {
                globalBus.$on('nodeDataRenew', this.setNodeData);
            },
            setNodeData(node, id) {
                this.nodeList.forEach((item, i) => {
                    if (item.id === id) { //监听编辑面板的数据，修改node
                        item.widget = this.getWidget(node, item);
                    }
                });
            },
            renewStyle() {
                globalBus.$on('nodeDataStyle', this.setNodeStyle);
            },
            setNodeStyle(style, id) {
                const $this = this;
                this.nodeList.forEach(item => {
                    if (item.id === id) { //监听编辑面板的数据，修改node
                        item.style = style;
                        $this.$refs['chart' + id][0].setChartExtend(item.style);
                        item.widget.style = JSON.stringify(item.style);
                    }
                })
            },
            getWidget: function (chartData, item) {
                const vm = this;
                let c_data = JSON.parse(JSON.stringify(chartData));
                let code = chartData.dataset ? chartData.dataset.belongType === 'PHYSICAL' && chartData.dataset.dbType !== 'FILE' ? chartData.dataset.tableCode : chartData.dataset.globalCode : '';
                // let code = chartData.dataset ? chartData.dataset.globalCode : '';
                let ydims = vm.getDim(chartData, 'yData'), zdims = vm.getDim(chartData, 'zData'),
                    udims = vm.getDim(chartData, 'uData');
                let allDims = [...ydims, ...zdims, ...udims].map((d, inx) => {
                    d.index = inx;
                    return d;
                });
                let xmes = vm.getMeasures(chartData, 'xData'), kmes = vm.getMeasures(chartData, 'kData');
                let allMes = [...xmes, ...kmes].map((d, inx) => {
                    d.index = inx;
                    return d;
                });
                let relationConfigureJson ;
                if (item.code === 'BusinessRelationshipWidget') {
                    let  dateTime = new Date(chartData.startDate[1]).setDate(new Date(chartData.startDate[1]).getDate()+1);
                    let endTimeValue1 = new Date(dateTime).Format("yyyyMMdd");
                    let idNumber = chartData.idCard.toString(),
                        idNumberField = vm.getDim(chartData, 'idNumberFieldData'),
                        startTime = vm.getDim(chartData, 'startTimeData'),
                        endTime = vm.getDim(chartData, 'endTimeData'),
                        timeLabelFiled = vm.getDim(chartData, 'timeStampData'),
                        businessField = vm.getDim(chartData, 'businessColData') ,
                        timeInterval = vm.getDim(chartData, 'timeIntervalData'),
                        peerTypes = chartData.peerClass,
                        timeIntervalValue = chartData.timeIntervalValue,
                        timeIntervalUnit = chartData.timeInterUnit,
                        startTimeValue = (new Date(chartData.startDate[0])).Format("yyyyMMdd"),
                        endTimeValue = endTimeValue1;
                    relationConfigureJson = {
                        idNumber: idNumber,
                        idNumberField: idNumberField[0],
                        startTime: startTime[0],
                        endTime: endTime[0],
                        timeLabelFiled: timeLabelFiled,
                        businessField: businessField[0],
                        timeInterval: timeInterval[0],
                        peerTypes: peerTypes,
                        timeIntervalValue : timeIntervalValue,
                        timeIntervalUnit : timeIntervalUnit,
                        startTimeValue: startTimeValue,
                        endTimeValue: endTimeValue,
                    };
                };
                let widget = {
                    x: item.left,
                    y: item.top,
                    w: item.width,
                    h: item.height,
                    name: chartData.label,
                    title: chartData.style && chartData.style.title.text ? chartData.style.title.text : chartData.label,
                    refreshVal: chartData.refreshVal,
                    refreshUnit: chartData.refreshUnit,
                    widgetMetaId: item.widgetMeta.id,
                    widgetDataset: {
                        classifierStatId: chartData.classifierStatId,
                        datasetId: chartData.datasetId,
                        datasourceId: chartData.dataset ? chartData.dataset.dataSourceId : '',
                        tableCode: code,
                        dbType: chartData.dataset ? chartData.dataset.dbType : '',
                        widgetDatasetDims: allDims,
                        widgetDatasetMeasures: allMes,
                        dataArticleNumber : chartData.limit !== undefined ? chartData.limit : "",
                        relationType: item.code === 'BusinessRelationshipWidget' ?chartData.relationClass:"",
                        relationConfigureJson: item.code === 'BusinessRelationshipWidget' ? JSON.stringify(relationConfigureJson) :""
                    },
                    previewLine:chartData.rows,
                    style: JSON.stringify(item.style)
                };
                if (chartData.drill) {
                    let d_set = [];
                    chartData.drill.forEach(dri => {
                        let drill_data = vm.getDim(dri, 'options');
                        d_set.push(drill_data);
                    });
                    c_data.drillDataset = widget.drillDataset = d_set;
                }
                let filter = this.getFilter(chartData);
                if (filter && filter.length > 0) {
                    widget.filter = JSON.stringify(this.getFilter(chartData));
                }
                if (chartData.filterList && chartData.filterList.length > 0) {
                    widget.query = JSON.stringify(chartData.filterList);
                }
                if (chartData.linkage) {
                    widget.linkage = JSON.stringify(chartData.linkage);
                }
                if(chartData.jumpInfo) {
                    widget.jumpInfo = JSON.stringify(chartData.jumpInfo);
                }
                let beforeData = JSON.parse(JSON.parse(item.widget).beforeData);
                widget.beforeData = JSON.stringify(Object.assign(beforeData, c_data));
                return JSON.stringify(widget);
            },
            getMeasuresOpt(data) {

            },
            getIsDistinct(d) {
                let v = d.duplicateF && d.duplicateF.value;
                if (v) {
                    return 1;
                }
                return 0
            },
            getDim(chartData, key) {
                let widgetDatasetDims = [];
                if (!chartData || !chartData[key]) {
                    return widgetDatasetDims;
                }
                for (let i = 0; i < chartData[key].length; i++) {
                    let d = chartData[key][i];
                    let dim = {
                        widgetDatasetId: chartData.datasetId,
                        filedId: d.data.id,
                        filedName: d.data.label,
                        filedCode: d.data.code,
                        filedAlias: d.data.alias || d.data.code,
                        type: d.data.type,
                        index: i,
                        orderBy: this.getOrderByRule(d.rank.value),
                        formatDecimals: this.getFormatDecimals(d.format),
                        isSeparator: this.getIsSeparator(d.format),
                        formatTime: this.getFormatTime(d.format),
                        filterType: this.getFilterType(d.format),
                        distinct: this.getIsDistinct(d),
                        visualTimeFormat: this.getAttribute(d.timeFormat),
                        dataSetTimeFormat : d.data.format ,
                        dataPolishingDateGranularity:this.getAttribute(d.dataMakeUp)
                    };
                    //设置别名
                    let code = dim.filedCode;
                    let alias = dim.filedCode;
                    //如果数据集字段编辑步骤添加别名，则查询字段为别名
                    if (d.data.alias !== "") {
                        code = d.data.alias;
                        alias = d.data.alias
                    }
                    //校验是否是聚合函数
                    let flag = this.isExp(d.data.exp);
                    if (flag) {
                        //聚合函数：可视化拼sql时必须有别名
                        code = d.data.exp;
                    }
                    dim.filedAlias = alias;
                    dim.filedCode = code;

                    widgetDatasetDims.push(dim);
                }
                return widgetDatasetDims;
            },
          /**
           * 判断获取属性值 若有none值，则取空
           * @param attr
           * @return {*|string}
           */
            getAttribute(attr){
              return attr && attr.value!== "none"  ? attr.value : "";
            },
            getFilterType(format) {
                /* if (format.value !== "") {
                     return format.value.type;
                 }*/
                return "";
            },
            getFormatDecimals(format) {
                /*if (format && format.value && format.value !== "") {
                    return format.value.decimal;
                }*/
                return 0;
            },
            getIsSeparator(format) {
                /* if (format && format.value && format.value !== "" && format.value.separator === true) {
                     return 1;
                 }*/
                return 0;
            },
            getFormatTime(format) {
                /*if (format && format.value && format.value !== "" && format.value.timeFormat && format.value.timeFormat !== "") {
                    return format.value.timeFormat;
                }*/
                return "";
            },
            getMeasures(chartData, axis) {
                const vm = this;
                let Measures = [];
                if (!chartData[axis]) {
                    return Measures;
                }
                Measures = vm.setMeasureData(chartData[axis], chartData.datasetId);
                Measures.forEach((item, inx) => {
                    item.index = inx;
                });
                return Measures;
            },
            setMeasureData(options, datasetId) {
                const vm = this;
                let Measures = [];
                for (let i = 0; i < options.length; i++) {
                    let d = options[i];
                    let ms = {
                        widgetDatasetId: datasetId,
                        filedId: d.data.id,
                        filedName: d.data.label,
                        filedCode: d.data.code,
                        filedAlias:d.data.alias || d.data.code,
                        type: d.data.type,
                        index: i,
                        isDivisor: d.isDivisor || null,
                        funcType: d.func&&d.func.value ||"none",
                        functionsJson: JSON.stringify(d.fastCount && d.fastCount.value ? d.fastCount.value === 'none' ? [] : [d.fastCount.value] : []),
                        orderBy: vm.getOrderByRule(d.rank.value),
                        formatDecimals: vm.getFormatDecimals(d.format),
                        isSeparator: vm.getIsSeparator(d.format),
                        formatTime: vm.getFormatTime(d.format),
                        filterType: vm.getFilterType(d.format),
                        isDistinct: vm.getIsDistinct(d),
                    };
                    //设置别名
                    let code = ms.filedCode;
                    let alias = ms.filedCode;
                    //如果数据集字段编辑步骤添加别名，则查询字段为别名
                    if (d.data.alias !== undefined && d.data.alias !== null && d.data.alias !== "") {
                        code = d.data.alias;
                        alias = d.data.alias
                    }
                    //校验是否是聚合函数
                    if (d.data.exp !== null && d.data.exp.indexOf("sum") > -1) {
                        //聚合函数：可视化拼sql时必须有别名
                        code = d.data.exp;
                    }
                    ms.filedAlias = alias;
                    ms.filedCode = code;
                    Measures.push(ms);
                    if (d.ratioOptions) {
                        let ms = vm.setMeasureData(d.ratioOptions, datasetId);
                        Array.prototype.push.apply(Measures, ms);
                    }
                }
                return Measures;
            },
            getOrderByRule(r) {
                if (r === "none") {
                    return "";
                }
                if (r === "descending") {
                    return "DESC";
                }
                if (r === "ascending") {
                    return "ASC";
                }
                return "";
            },

            getFilter: function (chartData) {
                let widgetFilters = [];
                if (!chartData.filterData) {
                    return widgetFilters;
                }
                for (let i = 0; i < chartData.filterData.length; i++) {
                    let af = chartData.filterData[i];
                    let field = {id: af.data.id, name: af.data.label, code: af.data.alias || af.data.code, jsType: af.data.type};
                    if (af.filterResult.parentType === "number") {
                        if (af.filterResult.value === "") {
                            continue;
                        }
                        widgetFilters.push({
                            field: field,
                            leftV: af.filterResult.value.leftV,
                            rightV: af.filterResult.value.rightV,
                            parentType: af.filterResult.parentType,
                            type: af.filterResult.type
                        })
                    } else if (af.filterResult.parentType === "time") {
                        if (af.filterResult.value === "") {
                            continue;
                        }
                        if (af.filterResult.type === "daterange" || af.filterResult.type === "monthrange") {
                            if (af.filterResult.tab === 'picker') {
                                widgetFilters.push({
                                    field: field,
                                    startTime: af.filterResult.value[0],
                                    endTime: af.filterResult.value[1],
                                    parentType: af.filterResult.parentType,
                                    type: af.filterResult.type
                                });
                            } else {

                                if (af.filterResult.type === "monthrange") {
                                    widgetFilters.push({
                                        field: field,
                                        value: af.filterResult.value.value,
                                        range: af.filterResult.value.range,
                                        year: af.filterResult.value.year,
                                        month: af.filterResult.value.month,
                                        parentType: af.filterResult.parentType,
                                        type: af.filterResult.type
                                    });
                                } else {
                                    widgetFilters.push({
                                        field: field,
                                        value: af.filterResult.value.value,
                                        range: af.filterResult.value.range,
                                        year: af.filterResult.value.year,
                                        month: af.filterResult.value.month,
                                        day: af.filterResult.value.day,
                                        parentType: af.filterResult.parentType,
                                        type: af.filterResult.type
                                    });
                                }
                            }
                            continue;
                        }

                        if (af.filterResult.type === "datetime") {
                            widgetFilters.push({
                                field: field,
                                value: af.filterResult.value,
                                parentType: af.filterResult.parentType,
                                type: af.filterResult.type
                            });
                            continue;
                        }

                        let startTime = af.filterResult.value[0];
                        let endTime = af.filterResult.value[1];
                        widgetFilters.push({
                            field: field,
                            startTime: startTime,
                            endTime: endTime,
                            parentType: af.filterResult.parentType,
                            type: af.filterResult.type
                        })
                    } else if (af.filterResult.parentType === "text") {
                        widgetFilters.push({
                            field: field,
                            value: af.filterResult.value,
                            parentType: af.filterResult.parentType,
                            type: af.filterResult.type
                        })
                    } else if (af.filterResult.parentType === "tree") {
                        widgetFilters.push({
                            field: field,
                            values: this.getTreeValues(af.filterResult.nodes),
                            parentType: af.filterResult.parentType,
                            type: af.filterResult.type
                        })
                    }
                }
                return widgetFilters;
            },
            getTreeValues(nodes) {
                let n = [];
                for (let i = 0; i < nodes.length; i++) {
                    let node = nodes[i];
                    let no = {code: node.value, name: node.name};
                    n.push(no);
                }
                return n;
            },

            async initData(row) {
                this.load = false;
                this.nodeList = row.data.map(list => {
                    if (list) {
                        // list.minW = list.minW > 1 ? 1 : list.minW;
                        // list.minH = list.minH > 5 ? 5 : list.minH;
                        list.x = list.left;
                        list.y = list.top;
                        list.w = list.width;
                        list.h = list.height;
                        list.i = list.id;
                        return list;
                    }
                }).filter(item => item !== undefined);
                this.reLoad();
            },

            fullScreen(isFull) {
                this.isFullScreen = isFull;
            },
            getPlugNode() {
                globalBus.$on('plugNode', this.setPlugNode);
            },
            setPlugNode(plug) {
                this.plugNode = plug;
                this.canDrop = true;
            },
            getTreeNode() {
                globalBus.$on('treeNode', this.setTreeNode);
            },
            setTreeNode(node) {
                this.plugNode = null;
            },
            unbindFn() {
                globalBus.$off('plugNode', this.setPlugNode);
                globalBus.$off('treeNode', this.setTreeNode);
                globalBus.$off('nodeDataRenew', this.setNodeData);
                globalBus.$off('nodeDataStyle', this.setNodeStyle);
                globalBus.$off('dragend', this.isAllow);
                globalBus.$off("rewNewFormList", this.rewNewFormList);
            },
            showAttrPanel(node) {
                this.$emit('showAttrPanel', true, node);
            },
            setParentBoundary() { //设置拖拽边界
                let $this = this;
                this.nodeList.forEach(node => {
                    $this.$refs['node_child' + node.id][0].checkParentSize();
                });
            },
            auto_position(width, nodeListData) {
                const vm = this;
                let nodeList;
                if (nodeListData) {
                    nodeList = nodeListData.nodes;
                } else {
                    nodeList = vm.nodeList.filter(no => no.show);
                }
                let x = 0, y = 0;
                let nodes = nodeList.sort(vm.compare('y', false));
                let maxH = 0, maxList = [];
                for (let i = 0; i < nodes.length; i++) {
                    let list = nodes[i];
                    if (list.y >= y) {
                        maxList.push(list);
                        y = list.y;
                    }
                }
                let res = maxList.sort(vm.compare('x', false));
                if (res.length) {
                    if (res[0].x + res[0].w <= 12 - width) {
                        x = res[0].x + res[0].w;
                        y = res[0].y;
                    } else {
                        x = 0;
                        y = res[0].y + res[0].h;
                    }
                }
                return {x, y};
            },
            addNode(e, isTab, tabId, tabVal, tab) {
                if(!this.canDrop) return;
                const vm = this, {visualServices, visualMock} = this;
                let services = vm.getServices(visualServices, visualMock);
                services.initWidget().then(res => {
                    if (res.data.status === 0) {
                        let id = res.data.data;
                        let width = vm.plugNode.code === "TabWidget" || vm.plugNode.code === "PGISWidget" ? 12 : vm.plugNode.code === "IndicatorCardWidget" ? 6 : vm.width;
                        let height = vm.plugNode.code === "TabWidget" ? 10 : vm.plugNode.code === "PGISWidget" ? 14 : vm.height;
                        let minH = vm.plugNode.code === "TextWidget" || vm.plugNode.code === "SelectWidget" ? 1 : 3;
                        let {x, y} = vm.auto_position(width, tab);
                        let nodeData = {
                            width: width,
                            height: height,
                            x: x,
                            y: y,
                            w: width,
                            h: height,
                            top: y,
                            left: x,
                            minW: 1,
                            minH: minH,
                            show: true,
                            // zIndex: index, //层级后期完善
                            label: vm.plugNode.label,
                            id: id,
                            i: id,
                            linkageId: id,
                            type: '',
                            title: vm.plugNode.label,
                            code: vm.plugNode.code,
                            widgetMeta: vm.plugNode.widgetMeta,
                            style: {}
                        };
                        let widget = {
                            x: x,
                            y: y,
                        };
                        if (isTab) {
                            nodeData.show = false;
                            widget.beforeData = JSON.stringify({show: false, isTabChart: true, tabId, tabVal});
                        } else {
                            widget.beforeData = JSON.stringify({show: true});
                        }
                        nodeData.widget = JSON.stringify(widget);
                        if (isTab) {
                            vm.$refs['chart' + tabId][0].setTabPanelNode(nodeData, tabVal);
                        }
                        vm.nodeList.push(nodeData);
                        vm.showAttrPanel(nodeData);
                        vm.$nextTick(() => {
                            let node = vm.nodeList.filter(node => node.id === id)[0];
                            vm.emitEditVisual(node);
                            vm.locationPanel(id);
                        })
                    }
                });
            },
            setTabListNode(tabId, nodeData, tabVal) {
                const vm = this;
                vm.nodeList.forEach(list => {
                    if (list.id === tabId) {
                        let widget = JSON.parse(list.widget), beforeData = JSON.parse(widget.beforeData);
                        beforeData.style.labelSetting.tabList.forEach(tab => {
                            if (tab.value === tabVal) {
                                tab.nodes.push(nodeData);
                            }
                        });
                        widget.beforeData = JSON.stringify(beforeData);
                        list.widget = JSON.stringify(widget);
                    }
                })
            },
            deleteTabListNode(tabId, nodeId, tabVal) {
                const vm = this;
                vm.nodeList.forEach(list => {
                    if (list.id === tabId) {
                        let widget = JSON.parse(list.widget), beforeData = JSON.parse(widget.beforeData);
                        beforeData.style.labelSetting.tabList.forEach(tab => {
                            if (tab.value === tabVal) {
                                tab.nodes = tab.nodes.filter(no => no.id !== nodeId);
                            }
                        });
                        widget.beforeData = JSON.stringify(beforeData);
                        list.widget = JSON.stringify(widget);
                    }
                })
            },
            allowDrop(e) {//判断放置的是可视化组件
                if (this.canDrop) {
                    e.preventDefault();
                }
            },
            getResizeNode(id, node) {
                this.currentId = id;
                this.zIndexChange(node)
            },
            resize(left, top, width, height) {
                let $this = this;
                this.nodeList.forEach(node => {
                    if (node.id === $this.currentId) {
                        node.width = width;
                        node.height = height;
                        node.top = top;
                        node.left = left;
                        node.widget.w = width;
                        node.widget.h = height;
                        node.widget.y = top;
                        node.widget.x = left;
                    }
                });
                this.$refs['chart' + this.currentId][0].chartResize();
            },
            drag(left, top) {
                let $this = this;
                this.nodeList.forEach(node => {
                    if (node.id === $this.currentId) {
                        node.top = top;
                        node.left = left;
                        node.widget.y = top;
                        node.widget.x = left;
                    }
                });
            },

            deleteNode(node) {
                const vm = this;
                this.confirm('删除', '确定要删除该面板?', () => {
                    vm.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                    vm.deleteOperate(node.id);
                    vm.$emit('closeAttr', '', node);
                })
            },
            deleteOperate(id) {
                this.nodeList = this.nodeList.filter(node => {
                    let widget = JSON.parse(node.widget), beforeData = JSON.parse(widget.beforeData);
                    return node.id !== id && beforeData.tabId !== id;
                })
            },
            editNode(id, nodeD) {
                this.showAttrPanel(nodeD);
                let node = this.nodeList.filter(node => node.id === id)[0];
                this.emitEditVisual(node);
                this.locationPanel(id);
            },
            emitEditVisual(node) {
                setTimeout(() => {
                    globalBus.$emit('editVisualPanel', node);
                }, 500)
            },
            rewNewFormList(list, nodeId) {
                const vm = this;
                vm.nodeList.forEach(item => {
                    if (item.id === nodeId) {
                        let widget = JSON.parse(item.widget), nodeData = JSON.parse(widget.beforeData);
                        nodeData.filterList = JSON.parse(JSON.stringify(list));
                        widget.beforeData = JSON.stringify(nodeData);
                        item.widget = JSON.stringify(widget);
                    }
                })
            },
          timeFormatFactory(type, format) {
            let formatCode = "yyyy-MM-dd",
                datFormat = ["yyyy-MM-dd", "yyyyMMdd", "yyyy/MM/dd"],
                formT = datFormat.indexOf(format);
            switch (type) {
              case "year" :
                formatCode = ["yyyy", "yyyy年", "yyyy"][formT];
                break;
              case "quarter":
                formatCode = ["yyyy-qq", "yyyy年qq季度", "yyyy/qq"][formT];
                break;
              case "month":
                formatCode = ["yyyy-MM", "yyyy年MM月", "yyyy/MM"][formT];
                break;
              case "week":
                formatCode = ["yyyy-WW", "yyyy年WW周", "yyyy/WW周"][formT];
                break;
              case "day":
                formatCode = ["yyyy-MM-dd", "yyyy年MM月dd日", "yyyy/MM/dd"][formT];
                break;
              default:
                break;
            }
            return formatCode;
          },
          addGlobalEvent(){
            globalBus.$on("changeWidgetMeta" , this.changeWidgetMeta)
          },
          removeGlobalEvent(){
            globalBus.$off("changeWidgetMeta" ,this.changeWidgetMeta);
          },
          changeWidgetMeta(node , plug){
              const vm = this;
              for(let item of this.nodeList){
                if(node.id === item.id) {
                  let widget = JSON.parse(item.widget) , beforeData = JSON.parse(widget.beforeData);
                  if(plug.code === "PieChartWidget" && beforeData && beforeData.yData && beforeData.yData.length > 1){
                    vm.$message.warning("基本饼图仅限一个维度字段!");
                    return ;
                  }
                  item.code = plug.code;
                  item.label = plug.label;
                  item.widgetMeta = JSON.parse(JSON.stringify(plug.widgetMeta));
                  vm.setBarOrLineDefaultGrid(beforeData.style);
                  widget.beforeData = JSON.stringify(beforeData);
                  item.widget = JSON.stringify(widget);
                  vm.editNode(node.id , item);
                }
              }
          },
          setBarOrLineDefaultGrid(style){
            if(!style || !style.legend) return;
            let {legend} = style;
            if(legend.top === 0 && legend.bottom === 0 && legend.left === 0 && legend.right === 0){
              this.radioChange(style.legend , style.legend.label_pos);
            }
          },
          /**
           * grid
           */
          radioChange (legend , val){
            this.setPosType(legend);
            if (val === 'left') {
              legend.left = 100;
              legend.right = 100;
              legend.bottom = 26;
              legend.top = 46;
            } else if (val === 'right') {
              legend.left = 40;
              legend.right = 120;
              legend.bottom = 26;
              legend.top = 46;
            } else if (val === 'top') {
              legend.left = 40;
              legend.right = 100;
              legend.bottom = 26;
              legend.top = 80;
            } else if (val === 'bottom') {
              legend.left = 40;
              legend.right = 100;
              legend.bottom = 50;
              legend.top = 46;
            }else {
              legend.left = 40;
              legend.right = 100;
              legend.bottom = 30;
              legend.top = 46;
            }
          },
          setPosType(legend){
            legend.left_type = legend.top_type = legend.bottom_type = legend.right_type = 'abs';
          },
            exportExcel(id, node){
                const vm = this;
                let layer = vm.$dgLayer({
                    title: '导出Excel',
                    content: require("./exportExcel"),
                    area: ["600px", "300px"],
                    props:{
                        total: vm.$refs['chart'+ id][0].total,
                        limitSize : vm.limitSize,
                        label: node.label,
                    },
                    move : false,
                    on:{
                        close(){
                            layer.close(layer.dialogIndex);
                        },
                        export(params){
                            let postData = {...params};
                            postData.code = node.code;
                            postData.data = JSON.parse(node.widget);
                            postData.type = vm.isView;
                            let beforeData = JSON.parse(postData.data.beforeData);
                            if (beforeData.filterList && beforeData.filterList.length > 0) {
                                let query = beforeData.filterList.filter(list => {
                                    return list.filter && list.filter.addVal !== "";
                                });
                                postData.data.query = JSON.stringify(query);
                            }
                            postData.data.pageSize = vm.$refs['chart'+node.id][0].paginationProps.pageSize;
                            postData.data.index = vm.$refs['chart'+node.id][0].paginationProps.currentPage;
                            let xhr = new XMLHttpRequest();
                            let url = vm.$axios.defaults.baseURL + "/widget/downloadExcel";
                            xhr.open('post', url);
                            xhr.setRequestHeader('Content-Type', 'application/json; charset=UTF-8'); //我用了json
                            xhr.responseType = 'blob' //以blob的形式接收数据，一般文件内容比较大
                            xhr.onload = function() {
                                let content = this.response; //Blob数据
                                let elink = document.createElement('a'); // 创建一个a标签用于下载
                                elink.download = postData.downLoadName + '.xlsx'; //规定被下载的超链接目标名字
                                elink.style.display = 'none'; //标签隐藏
                                let blob = new Blob([content]);
                                elink.href = URL.createObjectURL(blob); //规定链接指向的页面的URL
                                document.body.appendChild(elink);
                                elink.click(); //原生dom触发
                                URL.revokeObjectURL(elink.href); // 释放URL 对象
                                document.body.removeChild(elink);
                                layer.close(layer.dialogIndex);
                            }
                            xhr.send(JSON.stringify(postData));
                        }
                    }
                })
            },
            getDownloadSize(){
              const vm = this;
              let service = vm.$services("visual");
                service.getDownloadSize().then(res=>{
                    if(res.data.status == 0){
                        vm.limitSize = Number(res.data.data.limitSize);
                    }
                })
            }
        },
        beforeCreate() {
            document.querySelector('body').setAttribute('style', 'overflow:hidden')
        },
        beforeDestroy() {
            document.body.removeAttribute('style')
        },
        created() {
            this.getPlugNode();
            this.getTreeNode();
            this.renewNode();
            this.renewStyle();
            this.setCanDrop();
            globalBus.$on("rewNewFormList", this.rewNewFormList);
            this.addGlobalEvent();
            this.getDownloadSize();
        },
        mounted() {
            // 添加 resize 绑定事件
        },
        destroyed() {
            //解绑 resize
            this.unbindFn();
            this.removeGlobalEvent();
        }
    }
</script>

<style scoped lang="less">
    .visualPanel {
        position: relative;
        height: calc(100% - 76px);
        background-color: #f6faff;
        background-image: linear-gradient(90deg, rgba(10, 15, 15, 0.05) 10%, rgba(0, 0, 0, 0) 10%), linear-gradient(rgba(10, 15, 15, 0.05) 10%, rgba(0, 0, 0, 0) 10%);
        background-size: 10px 10px;
        overflow: auto;
        transition: 600ms;
    }

    .visualCont {
        width: calc(100vw - 20px);
        height: 100%;
        overflow: auto;
        position: relative;
    }

    .ce-full-screen {
        position: fixed;
        left: 9px;
        top: 50px;
        right: 9px;
        bottom: 10px;
        z-index: 1000;
        height: auto;
    }

</style>
<style lang="less" src="./visual.less"></style>
