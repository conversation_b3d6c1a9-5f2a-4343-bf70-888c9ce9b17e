<template>
    <div class="chartCard" :class="{'pt30': showDrill}">
        <!-- 切换按钮 -->
        <div class="chart-toggle-buttons" v-if="!loading && !data_empty">
            <el-button-group>
                <el-button
                    :type="displayMode === 'chart' ? 'primary' : 'default'"
                    size="mini"
                    @click="switchDisplayMode('chart')"
                    icon="el-icon-pie-chart">
                    饼图
                </el-button>
                <el-button
                    :type="displayMode === 'table' ? 'primary' : 'default'"
                    size="mini"
                    @click="switchDisplayMode('table')"
                    icon="el-icon-s-grid">
                    表格
                </el-button>
            </el-button-group>
        </div>

        <!-- 饼图显示 -->
        <div v-show="displayMode === 'chart'" :style="{'height' : showDrill ? drillHeight : height}">
            <ve-pie
                    v-loading="loading"
                    :width="width"
                    :height="height"
                    :data="chartData"
                    :settings="chartSettings"
                    :extend="extend"
                    :judge-width="true"
                    :data-empty="data_empty"
                    :events="chartEvents"
                    ref="chart"
            >
            </ve-pie>
            <!-- :colors="colors"-->
        </div>

        <!-- 表格显示 -->
        <div v-show="displayMode === 'table'" class="table-container" :style="{'height': tableContainerHeight, 'max-height': height, 'overflow': 'auto'}">
            <common-table
                v-loading="loading"
                :data="tableData"
                :columns="tableColumns"
                :pagination="false"
                :border="true"
                :stripe="true"
                :show-header="true"
                class="pie-statistics-table"
                :max-height="tableMaxHeight"
                :row-class-name="getRowClassName"
                :header-row-class-name="'table-header-row'"
                :header-cell-class-name="'table-header-cell'"
            >
                <!-- 自定义百分比列 -->
                <template slot="percentage" slot-scope="{row}">
                    <div class="percentage-cell">
                        <span class="percentage-text">{{ row.percentage }}%</span>
                        <div class="percentage-bar">
                            <div
                                class="percentage-fill"
                                :style="{ width: row.percentage + '%', backgroundColor: row.color }"
                            ></div>
                        </div>
                    </div>
                </template>

                <!-- 自定义颜色指示器列 -->
                <template slot="colorIndicator" slot-scope="{row}">
                    <div class="color-indicator">
                        <span
                            class="color-dot"
                            :style="{ backgroundColor: row.color }"
                            v-if="!row.isInnerDetail"
                        ></span>
                    </div>
                </template>

                <!-- 自定义外层分类列 -->
                <template slot="outerCategory" slot-scope="{row}">
                    <span :class="{'outer-category': row.isOuterTotal, 'inner-detail': row.isInnerDetail}">
                        {{ row.outerCategory }}
                    </span>
                </template>

                <!-- 自定义内层分类列 -->
                <template slot="innerCategory" slot-scope="{row}">
                    <span :class="{'outer-total': row.isOuterTotal, 'inner-detail': row.isInnerDetail}">
                        {{ row.innerCategory }}
                    </span>
                </template>
            </common-table>
        </div>

        <div v-if="showDrill" class="chartTip"
             @click="backToDims"
             :title="dimsName"><i class="el-icon-arrow-left"></i>{{dimsName}}
        </div>
    </div>
</template>
<script>
    import 'v-charts/lib/style.css'
    import {globalBus} from "@/api/globalBus";
    import {chartMxins} from "./chart-mixins/chart-mixins";
    import numerify from 'numerify'

    export default {
        name: "PieCard",
        mixins: [chartMxins],
        data() {
            return {
                // 显示模式：'chart' 或 'table'
                displayMode: 'chart',
                // 表格数据
                tableData: [],
                // 表格列配置
                tableColumns: [
                    {
                        prop: 'colorIndicator',
                        label: '',
                        width: 50,
                        align: 'center'
                    },
                    {
                        prop: 'name',
                        label: '分类',
                        minWidth: 120,
                        align: 'left'
                    },
                    {
                        prop: 'value',
                        label: '数值',
                        minWidth: 100,
                        align: 'right'
                    },
                    {
                        prop: 'percentage',
                        label: '占比',
                        minWidth: 150,
                        align: 'center'
                    }
                ],
                extend: {
                    series: {},
                    title: {
                        show: true,
                        top: 10,
                        left: 'center',
                        textStyle: {
                            color: '#666'
                        }
                    },
                    tooltip: {
                        show: true,
                        trigger: 'item',
                        appendToBody: true
                    },
                    legend: {
                        top: 45,
                        left: 'center',
                    },
                },
              defaultLegend : {
                color_type :"custom",
                color_theme :"default",
                color: [],
                radius_o: 50,
                radius_i: 0,
                out_radius_o: 80,
                out_radius_i: 65,
                radius_o_type: "abs",
                radius_i_type: "abs",
                out_radius_o_type: "abs",
                out_radius_i_type: "abs",
                label_show: true,
                label_pos: "outside",
                orient: "horizontal",
                legend_pos: "top",
                measure: "",
                label_cont: "dimension,percent",
                top_type: "abs",
                top: 0,
                seriesName: [],
                bottom_type: "abs",
                bottom: 0,
                left_type: "abs",
                left: 0,
                right_type: "abs",
                right: 0,
              }
            }
        },
        computed: {
            /**
             * 计算表格最大高度
             * 考虑切换按钮、标题、图例等元素的高度
             */
            tableMaxHeight() {
                // 基础高度减去各种元素占用的空间
                let baseHeight = '100%';
                let deductHeight = 50; // 切换按钮和内边距的基础高度

                if (this.showDrill) {
                    deductHeight += 30; // 钻取提示的高度
                }

                return `calc(${baseHeight} - ${deductHeight}px)`;
            },

            /**
             * 计算表格容器的实际高度
             * 用于设置表格容器的最大高度，确保滚动条正常工作
             */
            tableContainerHeight() {
                if (this.showDrill) {
                    return 'calc(100% - 30px)';
                }
                return '100%';
            }
        }
        ,
        methods: {
            /**
             * 切换显示模式
             * @param {string} mode - 显示模式：'chart' 或 'table'
             */
            switchDisplayMode(mode) {
                this.displayMode = mode;
                if (mode === 'table') {
                    this.generateTableData();

                    // 在表格渲染完成后检查是否需要滚动条
                    this.$nextTick(() => {
                        this.checkTableNeedsScroll();
                    });
                }
            },

            /**
             * 生成表格数据
             */
            generateTableData() {
                if (!this.chartData || !this.chartData.rows) {
                    this.tableData = [];
                    return;
                }

                const vm = this;
                let {beforeData} = JSON.parse(vm.node.widget);
                let nodeData = JSON.parse(beforeData);

                // 判断是否为嵌套饼图
                let isNestedPie = vm.node.code === 'RingPieChartWidget' && nodeData.yData.length > 1 && !vm.showDrill;

                if (isNestedPie) {
                    // 处理嵌套饼图数据格式
                    this.generateNestedPieTableData(nodeData);
                } else {
                    // 处理普通饼图数据格式
                    this.generateNormalPieTableData(nodeData);
                }
            },

            /**
             * 生成普通饼图表格数据
             * @param {Object} nodeData - 节点数据
             */
            generateNormalPieTableData(nodeData) {
                if (!this.chartData.columns) {
                    this.tableData = [];
                    return;
                }

                const vm = this;

                // 获取维度和度量字段信息
                let dimensionColumn = this.chartData.columns[0]; 
                let measureColumn = this.chartData.columns[1];   

                // 获取度量字段的别名
                let measureField = nodeData.xData && nodeData.xData[0];
                let measureLabel = measureField ? (measureField.alias || measureField.label) : measureColumn;

                // 获取维度字段的别名
                let dimensionField = nodeData.yData && nodeData.yData[0];
                let dimensionLabel = dimensionField ? (dimensionField.alias || dimensionField.label) : dimensionColumn;

                // 更新表格列标题
                this.tableColumns[1].label = dimensionLabel; // 分类列
                this.tableColumns[2].label = measureLabel;   // 数值列

                // 计算总值用于百分比计算
                let totalValue = 0;
                this.chartData.rows.forEach(row => {
                    let value = parseFloat(row[measureColumn] || 0);
                    totalValue += value;
                });

                // 生成表格数据
                this.tableData = this.chartData.rows.map((row, index) => {
                    let rawValue = parseFloat(row[measureColumn] || 0);
                    let percentage = totalValue > 0 ? ((rawValue / totalValue) * 100).toFixed(2) : 0;
                    let formattedValue = rawValue;

                    // 应用数字格式化
                    if (measureField && measureField.data && measureField.data.numberFormat && measureField.data.numberFormat !== "none") {
                        formattedValue = numerify(rawValue, vm.format[measureField.data.numberFormat]);
                    }

                    return {
                        name: row[dimensionColumn] || '', // 使用维度列的值作为名称
                        value: formattedValue,
                        originalValue: rawValue,
                        percentage: parseFloat(percentage),
                        color: this.getItemColor(index),
                        colorIndicator: ''
                    };
                });

                // 按数值降序排序
                this.tableData.sort((a, b) => parseFloat(b.originalValue) - parseFloat(a.originalValue));
            },

            /**
             * 生成嵌套饼图表格数据
             * @param {Object} nodeData - 节点数据
             */
            generateNestedPieTableData(nodeData) {
                const vm = this;

                // 检查数据结构
                if (!vm.chartData || !vm.chartData.dimsCodes) {
                    console.warn('Invalid chartData structure for nested pie');
                    vm.tableData = [];
                    return;
                }

                // 获取字段信息
                let outerDimension = vm.chartData.dimsCodes[0]; 
                let innerDimension = vm.chartData.dimsCodes[1]; 
                let measureLabel = vm.chartData.measures; 

                // 获取字段别名
                let outerField = nodeData.yData && nodeData.yData[0];
                let innerField = nodeData.yData && nodeData.yData[1];
                let measureField = nodeData.xData && nodeData.xData[0];

                let outerLabel = outerField ? (outerField.alias || outerField.label) : outerDimension;
                let innerLabel = innerField ? (innerField.alias || innerField.label) : innerDimension;
                let measureDisplayLabel = measureField ? (measureField.alias || measureField.label) : measureLabel;

                // 更新表格列配置为嵌套结构
                this.tableColumns = [
                    {
                        prop: 'colorIndicator',
                        label: '',
                        width: 50,
                        align: 'center'
                    },
                    {
                        prop: 'outerCategory',
                        label: outerLabel,
                        minWidth: 100,
                        align: 'left'
                    },
                    {
                        prop: 'innerCategory',
                        label: innerLabel,
                        minWidth: 120,
                        align: 'left'
                    },
                    {
                        prop: 'value',
                        label: measureDisplayLabel,
                        minWidth: 80,
                        align: 'right'
                    },
                    {
                        prop: 'percentage',
                        label: '占比',
                        minWidth: 120,
                        align: 'center'
                    }
                ];

                // 计算总值 - 需要从原始数据中获取
                let totalValue = 0;
                let sourceRows = vm.chartData.rows || [];

                // 如果是处理后的数据结构，需要从原始行数据中计算
                if (sourceRows.length > 0 && sourceRows[0].name && sourceRows[0].value !== undefined) {
                    // 直接从原始数据结构计算
                    sourceRows.forEach(row => {
                        totalValue += parseFloat(row.value || 0);
                    });
                } else {
                    // 如果数据结构不对，尝试从其他地方获取
                    console.warn('Unexpected data structure in generateNestedPieTableData');
                    return;
                }

                // 生成嵌套表格数据
                let tableData = [];

                sourceRows.forEach((row, outerIndex) => {
                    let outerValue = parseFloat(row.value || 0);
                    let outerPercentage = totalValue > 0 ? ((outerValue / totalValue) * 100).toFixed(2) : 0;

                    // 添加外层汇总行
                    tableData.push({
                        outerCategory: row.name,
                        innerCategory: `【${row.name}小计】`,
                        value: this.formatValue(outerValue, measureField),
                        originalValue: outerValue,
                        percentage: parseFloat(outerPercentage),
                        color: this.getItemColor(outerIndex),
                        colorIndicator: '',
                        isOuterTotal: true
                    });

                    // 添加内层明细行
                    if (row.child && typeof row.child === 'object') {
                        Object.keys(row.child).forEach((childKey, childIndex) => {
                            let childValue = parseFloat(row.child[childKey] || 0);
                            let childPercentage = totalValue > 0 ? ((childValue / totalValue) * 100).toFixed(2) : 0;

                            tableData.push({
                                outerCategory: '', // 内层行不显示外层分类
                                innerCategory: `　├ ${childKey}`,
                                value: this.formatValue(childValue, measureField),
                                originalValue: childValue,
                                percentage: parseFloat(childPercentage),
                                color: this.getItemColor(outerIndex),
                                colorIndicator: '',
                                isInnerDetail: true
                            });
                        });
                    }
                });

                this.tableData = tableData;
            },

            /**
             * 格式化数值
             * @param {number} value - 原始数值
             * @param {Object} measureField - 度量字段配置
             * @returns {string} 格式化后的数值
             */
            formatValue(value, measureField) {
                const vm = this;
                if (measureField && measureField.data && measureField.data.numberFormat && measureField.data.numberFormat !== "none") {
                    return numerify(value, vm.format[measureField.data.numberFormat]);
                }
                return value;
            },

            /**
             * 获取表格行的样式类名
             * @param {Object} row - 行数据
             * @param {number} rowIndex - 行索引
             * @returns {string} 样式类名
             */
            getRowClassName({row, rowIndex}) {
                if (row.isOuterTotal) {
                    return 'outer-total-row';
                } else if (row.isInnerDetail) {
                    return 'inner-detail-row';
                }
                return '';
            },

            /**
             * 检查表格是否需要滚动条
             * @returns {boolean} 是否需要滚动条
             */
            checkTableNeedsScroll() {
                this.$nextTick(() => {
                    const tableContainer = this.$el.querySelector('.table-container');
                    const table = this.$el.querySelector('.pie-statistics-table .el-table');

                    if (tableContainer && table) {
                        const containerHeight = tableContainer.clientHeight;
                        const tableHeight = table.scrollHeight;

                        // 如果表格内容高度超过容器高度，添加滚动提示类
                        if (tableHeight > containerHeight) {
                            tableContainer.classList.add('has-scroll');
                        } else {
                            tableContainer.classList.remove('has-scroll');
                        }
                    }
                });
            },

            /**
             * 获取项目颜色
             * @param {number} index - 项目索引
             * @returns {string} 颜色值
             */
            getItemColor(index) {
                // 如果有自定义颜色配置，使用自定义颜色
                if (this.colors && this.colors.length > 0) {
                    return this.colors[index % this.colors.length];
                }

                // 默认颜色配置
                const defaultColors = [
                    '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
                    '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#ff9f7f',
                    '#ffdb5c', '#37a2ff', '#32c5e9', '#67e0e3', '#9fe6b8'
                ];

                return defaultColors[index % defaultColors.length];
            },

            /**
             * 重写图例自动布局方法，优化饼图的标题和图例间距
             * @param {Object} legend - 图例配置
             */
            legendAuto(legend) {
                const vm = this;
                let show_l = legend.legend_pos !== 'none';
                let left = '', top = '', zoomBottom;

                if (legend.legend_pos !== 'none') {
                    if (legend.legend_pos === 'left') {
                        left = 'left';
                        top = 'middle';
                        zoomBottom = 6;
                    } else if (legend.legend_pos === 'right') {
                        left = 'right';
                        top = 'middle';
                        zoomBottom = 6;
                    } else if (legend.legend_pos === 'top') {
                        left = 'center';
                        // 为饼图优化：如果有标题，图例位置下移更多
                        top = vm.extend.title.show ? 50 : 30;
                        zoomBottom = 6;
                    } else if (legend.legend_pos === 'bottom') {
                        left = 'center';
                        top = 'bottom';
                        zoomBottom = 30;
                    }
                } else {
                    zoomBottom = 6;
                }

                if (vm.extend.dataZoom) {
                    vm.extend.dataZoom[1].bottom = zoomBottom;
                }

                vm.extend.legend = {
                    type: 'scroll',
                    orient: legend.orient,
                    show: show_l,
                    left: left,
                    top: top,
                    align: 'auto',
                    height: "80%",
                    width: "80%"
                };
            },

            /**
             * 重写标题设置方法，优化饼图的标题位置
             * @param {Object} title - 标题配置
             */
            setTitle(title) {
                const vm = this, {extend} = this;
                extend.title.show = title.show;
                extend.title.text = title.text;

                if (title.custom_style === 'custom') {
                    let fStyle = title.fontStyle.length ? title.fontStyle[0] : "normal";
                    extend.title.textStyle = {
                        color: title.color,
                        fontFamily: title.fontFamily,
                        fontSize: parseInt(title.fontSize),
                        fontWeight: title.fontWeight,
                        fontStyle: fStyle
                    };
                    extend.title.left = title.align;
                    // 为饼图设置合适的标题位置
                    extend.title.top = 15;
                } else {
                    extend.title.textStyle = {
                        color: '#666',
                        fontSize: 16
                    };
                    extend.title.left = 'center';
                    extend.title.top = 15;
                    extend.title.textStyle.fontStyle = "normal";
                }
            },

            getNodeData() {
                globalBus.$on('setPiePanel', this.setHistogramPanel);
            },
            setChartExtend(settings) {
                this.setTitle(settings.title);
                this.setAlias(settings.fieldSetting);
                this.setLegend(settings.legend);
                this.setTooltip();
            },
            getDefaultLegend(legend , nodeData){
              const vm = this;
              if(legend.label_cont === undefined || legend.radius_i === undefined){
                vm.defaultLegend.seriesName = [];
                nodeData.yData.forEach(item => {
                  vm.defaultLegend.seriesName.push(item.label);
                })
                vm.defaultLegend.measure = nodeData.xData.length && nodeData.xData[0].label;
                return Object.assign(legend , this.defaultLegend);
              }else {
                return legend;
              }
            },
            setLegend(legend) {
                const vm = this;
                let {beforeData} = JSON.parse(vm.node.widget),
                    nodeData = JSON.parse(beforeData);
                let de_legend = vm.getDefaultLegend(legend , nodeData);
                vm.setColor(de_legend.color, de_legend.color_theme, de_legend.color_type);
                vm.setDataLabel(de_legend.label_show, de_legend.label_pos, nodeData);
                vm.legendAuto(de_legend);
                vm.setSeriesM(de_legend, nodeData);
                vm.setLabelCont(de_legend.label_cont, de_legend.seriesName, de_legend.measure, nodeData);
                vm.setRadius(de_legend, de_legend.radius_i, de_legend.radius_o, de_legend.out_radius_i, de_legend.out_radius_o, nodeData);
            },
            setTooltip() {
                const node = this.node, vm = this;
                this.extend.tooltip.formatter = function (params) {
                    let {beforeData} = JSON.parse(vm.node.widget),
                        nodeData = JSON.parse(beforeData);
                    let yField = nodeData.yData.filter(item => item.label === params.seriesName);
                    let funcLabel = nodeData.xData[0].func.name;
                    let xLabel = nodeData.xData[0].alias || nodeData.xData[0].label;
                    let xField = vm.chartData.rows[0][nodeData.xData[0].data.code];
                    let yLabel = yField[0].alias || yField[0].label;
                    if (vm.chartData && vm.chartData.dimsCodes) {
                       if(vm.node.code === "RingPieChartWidget"){
                           let inx = params.seriesIndex === 0 ? 1 : 0;
                           yLabel = vm.chartData.dimsCodes[inx];
                       }else {
                           yLabel = vm.chartData.dimsCodes[0];
                       }
                    }
                    let result;
                    let paramsVal = nodeData.xData[0].data.numberFormat && nodeData.xData[0].data.numberFormat !== "none" ? numerify(params.value , vm.format[nodeData.xData[0].data.numberFormat]) : params.value;
                    result = `<div>\
                                    <div class="f14 mb5">${yLabel} :</div>\
                                    <span class="dib vm " style="width:14px;height: 14px;border-radius:4px;background:${params.color};"></span>\
                                     <span>${params.name} : ${paramsVal}(${xLabel})</span>\
                                     <span>[${params.percent}%] (${funcLabel})  </span>
                                </div>`;
                    return result;
                };
            },
            /**
             * 设置数据标签
             * @param conts 显示的内容
             * @param seriesName 图表系列名
             * @param measure 度量
             * @param nodeData 图表数据
             * d_cont 维度内容 ， m_cont 度量内容 p_cont 百分比 cont1 数据名 ，cont2 数据值
             * */
            setLabelCont(conts, seriesName, measure, nodeData) {
                const vm = this, {chartData} = this;
                let d_cont = "", m_cont = "", p_cont = "";
                let condition = vm.node.code === 'RingPieChartWidget' && nodeData.yData.length > 1 && !vm.showDrill;
                let seriesN;
                if (condition) {
                    vm.extend.series[0].name = seriesName[1];
                    vm.extend.series[1].name = seriesName[0];
                    seriesN = seriesName[1];
                } else {
                    if (Array.isArray(seriesName)) {
                        vm.extend.series.name = seriesName[0];
                        seriesN = seriesName[0];
                    } else {
                        vm.extend.series.name = seriesName;
                        seriesN = seriesName;
                    }
                }
                let labelM = vm.chartSettings.labelMap,
                    d_n = labelM[seriesN] ? labelM[seriesN] : seriesN,
                    m_n = labelM[measure] ? labelM[measure] : measure;
                if (vm.node.code !== 'RingPieChartWidget') {
                    if(vm.filterDimsName) d_n = vm.filterDimsName;
                } else {
                    if(vm.filterDimsName && vm.chartData.dimsCodes.indexOf(d_n) === -1) d_n = vm.filterDimsName;
                }

                if (conts && conts.length) {
                    conts.split(",").forEach(item => {
                        if (item === "dimension") {
                            d_cont = `(${d_n})`;
                        } else if (item === "measure") {
                            m_cont = `(${m_n})`;
                        } else if (item === "percent") {
                            p_cont = "({d}%)";
                        }
                    });
                }
                if (condition) {
                    vm.extend.series[0].label.formatter = function (params) {
                        let value = nodeData.xData[0].data.numberFormat && nodeData.xData[0].data.numberFormat !== "none" ? numerify(params.data.value , vm.format[nodeData.xData[0].data.numberFormat])  : params.data.value;
                        let percent = p_cont ? ' ('+ params.percent + "%)" : p_cont;
                        return params.name + d_cont + ':' + value + m_cont + percent;
                    }
                } else {
                    vm.extend.series.label.formatter = function (params) {
                        let value = nodeData.xData[0].data.numberFormat && nodeData.xData[0].data.numberFormat !== "none" ? numerify(params.data.value , vm.format[nodeData.xData[0].data.numberFormat])  : params.data.value;
                        let percent = p_cont ? ' ('+ params.percent + "%)" : p_cont;
                        return params.name + d_cont + ':' + value + m_cont + percent;
                    }
                }

            },
            setSeriesM(legend, nodeData) {
                const vm = this;
                let top = legend.top_type === "abs" ? legend.top : legend.top + '%',
                    bottom = legend.bottom_type === "abs" ? legend.bottom : legend.bottom + '%',
                    left = legend.left_type === "abs" ? legend.left : legend.left + '%',
                    right = legend.right_type === "abs" ? legend.right : legend.right + '%';
                if (vm.node.code === 'RingPieChartWidget' && nodeData.yData.length > 1 && !vm.showDrill) {
                    vm.extend.series[0].top = top;
                    vm.extend.series[0].bottom = bottom;
                    vm.extend.series[0].left = left;
                    vm.extend.series[0].right = right;
                    vm.extend.series[1].top = top;
                    vm.extend.series[1].bottom = bottom;
                    vm.extend.series[1].left = left;
                    vm.extend.series[1].right = right;
                } else {
                    vm.extend.series.top = top;
                    vm.extend.series.bottom = bottom;
                    vm.extend.series.left = left;
                    vm.extend.series.right = right;
                }
                this.tipWidth = legend.left_type === "abs" ? legend.left + 100 + 'px' : legend.left + '% + 100px';
            },
            setRadius(legend, ins, out, o_ins, o_out, nodeData) {
                const vm = this;
                let ins_v = legend.radius_i_type === "abs" ? ins : ins + '%',
                    out_v = legend.radius_o_type === "abs" ? out : out + '%',
                    o_ins_v = legend.out_radius_i_type === "abs" ? o_ins : o_ins + '%',
                    o_out_v = legend.out_radius_o_type === "abs" ? o_out : o_out + '%';
                if (vm.node.code === 'RingPieChartWidget' && nodeData.yData.length > 1 && !vm.showDrill) {
                    vm.extend.series[0].radius = [o_ins_v, o_out_v];
                    if (vm.extend.series[1]) {
                        vm.extend.series[1].radius = [ins_v, out_v];
                    }
                } else {
                    vm.extend.series.radius = [ins_v, out_v];
                }
            },
            setDataLabel(show, pos, nodeData) {
                const vm = this;
                if (vm.node.code === 'RingPieChartWidget' && nodeData.yData.length > 1 && !vm.showDrill) {
                    vm.extend.series[0].label.show = show;
                    vm.extend.series[0].label.position = pos; //top inside insideBottom
                } else {
                    vm.extend.series.label.show = show;
                    vm.extend.series.label.position = pos; //top inside insideBottom
                }

            },
            setRingPieData(data) {
                const vm = this;
                let out_data = [], in_data = [];
                vm.chartSettings.level = [];
                data.rows.forEach(item => {
                    in_data.push({
                        name: item.name,
                        value: item.value
                    });
                    for (let k in item.child) {
                        out_data.push({
                            name: k,
                            value: item.child[k]
                        });
                    }

                });
                vm.extend.series = [];
                vm.extend.series[0] = {
                    name: "",
                    type: 'pie',
                    radius: ['40%', '55%'],
                    center: ['50%', '60%'], // 向下调整环形饼图外环中心位置
                    animationThreshold: 500,
                    avoidLabelOverlap: false,
                    minShowLabelAngle: 8,
                    label: {
                        show: true,
                        position: 'outside'
                    },
                    data: out_data
                };
                vm.extend.series[1] = {
                    name: "",
                    type: 'pie',
                    animationThreshold: 500,
                    avoidLabelOverlap: false,
                    minShowLabelAngle: 8,
                    radius: [0, '35%'],
                    center: ['50%', '60%'], // 向下调整环形饼图内环中心位置
                    label: {
                        normal: {
                            position: 'inner'
                        }
                    },
                    labelLine: {
                        normal: {
                            show: false
                        }
                    },
                    data: in_data
                };
            },
            /**
             * 设置嵌套饼图的行数据
             * @param {Object} datas - 原始数据
             * @returns {Array} 处理后的行数据
             */
            setRowsData(datas) {
                let rows = [];
                if (!datas || !datas.rows || !datas.columns || !datas.columns.dims) {
                    console.warn('Invalid data structure for setRowsData');
                    return rows;
                }

                datas.rows.forEach(item => {
                    let list = {};
                    if (datas.columns.dims && datas.columns.dims.length > 0) {
                        list[datas.columns.dims[0]] = item.name;
                    }
                    if (datas.columns.dims && datas.columns.dims.length > 1) {
                        list[datas.columns.dims[1]] = item.child;
                    }
                    if (datas.columns.measures) {
                        list[datas.columns.measures] = item.value;
                    }
                    rows.push(list);
                });
                return rows;
            },
            async getMessage(msg) {
                const vm = this;
                let result = JSON.parse(msg.data), data = result.data;
                if (vm.socket) vm.socket.close();
                let {beforeData} = JSON.parse(vm.node.widget),
                    nodeData = JSON.parse(beforeData);
                if (result.status === 0 && data) {
                    if (vm.node.code === 'RingPieChartWidget' && nodeData.yData.length > 1 && !vm.showDrill) {
                        vm.setRingPieData(data);

                        // 保存原始数据结构，确保嵌套饼图的数据可以正确访问
                        vm.chartData = {
                            dimsCodes: data.dimsCodes,
                            dims: data.columns.dims,
                            measures: data.columns.measures,
                            rows: data.rows, // 直接使用原始行数据，不进行转换
                            originalData: data // 保存完整的原始数据
                        };
                    } else {
                        vm.chartData = data;
                        delete vm.chartSettings.level;
                        vm.extend.series = {
                            name: data.columns[1],
                            type: "pie",
                            radius: [0, 0],
                            center: ['50%', '60%'], // 向下调整饼图中心位置，为标题和图例留出空间
                            animationThreshold: 500,
                            avoidLabelOverlap: false,
                            minShowLabelAngle: 8,
                            label: {
                                show: true,
                                position: 'outside'
                            },
                        };
                    }
                    vm.setChartExtend(nodeData.style);
                    vm.set_digital_format(nodeData.xData);
                    vm.data_empty = false; //没有数据为true
                    vm.loading = false;

                    // 如果当前是表格模式，生成表格数据
                    if (vm.displayMode === 'table') {
                        vm.generateTableData();

                        // 检查表格是否需要滚动条
                        vm.$nextTick(() => {
                            vm.checkTableNeedsScroll();
                        });
                    }
                } else {
                    vm.data_empty = true; //没有数据为true
                    vm.loading = false;
                    vm.chartData = {};
                    vm.tableData = []; // 清空表格数据
                }
            },
        }

    }
</script>

<style scoped lang="less" src="./css/chart.less"></style>

<style scoped lang="less">
/* 切换按钮样式 */
.chart-toggle-buttons {
    position: absolute;
    top: 5px;
    right: 10px;
    z-index: 10;

    .el-button-group {
        .el-button {
            padding: 5px 10px;
            font-size: 12px;
            border-radius: 4px;

            &:first-child {
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
            }

            &:last-child {
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
            }
        }
    }
}

/* 表格容器样式 */
.table-container {
    padding: 40px 10px 10px 10px;
    overflow: auto;
    position: relative;

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    &::-webkit-scrollbar-thumb {
        background-color: #c0c4cc;
        border-radius: 4px;

        &:hover {
            background-color: #909399;
        }
    }

    &::-webkit-scrollbar-track {
        background-color: #f5f7fa;
        border-radius: 4px;
    }

    /* 滚动提示样式 */
    &.has-scroll {
        &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 20px;
            background: linear-gradient(to bottom, rgba(255,255,255,0), rgba(245,247,250,0.8));
            pointer-events: none;
            z-index: 1;
        }
    }

    .pie-statistics-table {
        height: 100%;

        /deep/ .el-table {
            font-size: 12px;

            /* 表头固定样式 */
            .el-table__header-wrapper {
                position: sticky;
                top: 0;
                z-index: 2;
            }

            .el-table__header {
                th {
                    background-color: #f5f7fa;
                    color: #606266;
                    font-weight: 600;
                }
            }

            /* 表格内容样式 */
            .el-table__body {
                tr {
                    &:hover {
                        background-color: #f5f7fa;
                    }
                }

                td {
                    padding: 8px 0;
                }
            }

            /* 确保表格内容不会被截断 */
            .el-table__body-wrapper {
                overflow: visible;
            }
        }
    }

    /* 表头行样式 */
    /deep/ .table-header-row {
        background-color: #f5f7fa;
    }

    /* 表头单元格样式 */
    /deep/ .table-header-cell {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: 600;
    }
}

/* 百分比单元格样式 */
.percentage-cell {
    display: flex;
    align-items: center;
    gap: 8px;

    .percentage-text {
        min-width: 45px;
        font-weight: 600;
        color: #606266;
    }

    .percentage-bar {
        flex: 1;
        height: 8px;
        background-color: #e4e7ed;
        border-radius: 4px;
        overflow: hidden;

        .percentage-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }
    }
}

/* 颜色指示器样式 */
.color-indicator {
    display: flex;
    justify-content: center;
    align-items: center;

    .color-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 1px solid rgba(0, 0, 0, 0.1);
    }
}

/* 嵌套表格样式 */
.pie-statistics-table {
    /deep/ .outer-total-row {
        background-color: #f8f9fa !important;
        font-weight: 600;

        td {
            border-top: 2px solid #e9ecef;
        }
    }

    /deep/ .inner-detail-row {
        background-color: #fdfdfd !important;

        td {
            padding-left: 20px;
            font-size: 12px;
            color: #666;
        }
    }
}

.outer-category {
    font-weight: 600;
    color: #303133;
}

.outer-total {
    font-weight: 600;
    color: #409eff;
}

.inner-detail {
    color: #909399;
    font-size: 12px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .chart-toggle-buttons {
        top: 5px;
        right: 5px;

        .el-button-group .el-button {
            padding: 3px 6px;
            font-size: 11px;
        }
    }

    .table-container {
        padding: 35px 5px 5px 5px;

        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        /deep/ .el-table {
            font-size: 11px;

            .el-table__header th {
                padding: 6px 0;
            }

            .el-table__body td {
                padding: 6px 0;
            }
        }
    }

    .percentage-cell {
        flex-direction: column;
        gap: 4px;

        .percentage-text {
            min-width: auto;
        }

        .percentage-bar {
            width: 100%;
        }
    }

    .pie-statistics-table {
        /deep/ .inner-detail-row td {
            padding-left: 10px;
        }
    }
}
</style>
