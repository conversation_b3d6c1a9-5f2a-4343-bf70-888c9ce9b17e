<template>
    <div class="ring-pie-test">
        <h2>环形饼图表格功能限制测试</h2>
        
        <div class="test-grid">
            <!-- 普通饼图 - 有切换按钮 -->
            <div class="test-item">
                <h3>普通饼图 (PieChartWidget)</h3>
                <p class="test-description">✅ 显示切换按钮，支持表格模式</p>
                <div class="chart-container">
                    <PieCard 
                        :node="normalPieNode" 
                        :id="'normal-pie-test'"
                        :isView="false"
                    />
                </div>
            </div>
            
            <!-- 环形饼图 - 无切换按钮 -->
            <div class="test-item">
                <h3>环形饼图 (RingPieChartWidget)</h3>
                <p class="test-description">❌ 不显示切换按钮，仅支持图表模式</p>
                <div class="chart-container">
                    <PieCard 
                        :node="ringPieNode" 
                        :id="'ring-pie-test'"
                        :isView="false"
                    />
                </div>
            </div>
            
            <!-- 环形饼图 - 单维度 -->
            <div class="test-item">
                <h3>环形饼图 - 单维度</h3>
                <p class="test-description">❌ 不显示切换按钮</p>
                <div class="chart-container">
                    <PieCard 
                        :node="ringPieSingleNode" 
                        :id="'ring-pie-single-test'"
                        :isView="false"
                    />
                </div>
            </div>
            
            <!-- 环形饼图 - 多维度 -->
            <div class="test-item">
                <h3>环形饼图 - 多维度</h3>
                <p class="test-description">❌ 不显示切换按钮</p>
                <div class="chart-container">
                    <PieCard 
                        :node="ringPieMultiNode" 
                        :id="'ring-pie-multi-test'"
                        :isView="false"
                    />
                </div>
            </div>
        </div>
        
        <div class="feature-description">
            <h3>功能限制说明：</h3>
            <ul>
                <li>✅ <strong>普通饼图 (PieChartWidget)</strong>：显示切换按钮，支持图表/表格切换</li>
                <li>❌ <strong>环形饼图 (RingPieChartWidget)</strong>：不显示切换按钮，仅支持图表模式</li>
                <li>🔒 环形饼图的表格切换功能被完全禁用</li>
                <li>🎯 通过 <code>isRingPieChart</code> 计算属性控制显示逻辑</li>
                <li>⚡ 切换方法中添加了类型检查，防止误操作</li>
                <li>📊 数据生成方法中跳过环形饼图的表格数据处理</li>
            </ul>
        </div>
        
        <div class="code-example">
            <h3>实现代码：</h3>
            <pre><code>// 计算属性判断是否为环形饼图
computed: {
    isRingPieChart() {
        return this.node && this.node.code === 'RingPieChartWidget';
    }
}

// 切换按钮条件渲染
&lt;div v-if="!loading && !data_empty && !isRingPieChart"&gt;
    &lt;!-- 切换按钮 --&gt;
&lt;/div&gt;

// 表格显示条件
&lt;div v-show="displayMode === 'table' && !isRingPieChart"&gt;
    &lt;!-- 表格内容 --&gt;
&lt;/div&gt;</code></pre>
        </div>
    </div>
</template>

<script>
import PieCard from './PieCard.vue'

export default {
    name: 'PieCardRingTest',
    components: {
        PieCard
    },
    data() {
        return {
            // 普通饼图数据
            normalData: {
                columns: ["类别", "数值"],
                rows: [
                    { "类别": "类别A", "数值": 35 },
                    { "类别": "类别B", "数值": 25 },
                    { "类别": "类别C", "数值": 20 },
                    { "类别": "类别D", "数值": 15 },
                    { "类别": "类别E", "数值": 5 }
                ],
                dimsCodes: ["类别"],
                formatMapping: {}
            },
            
            // 环形饼图数据
            ringData: {
                columns: {
                    dims: ["category", "subcategory"],
                    measures: "value"
                },
                rows: [
                    {
                        name: "电子产品",
                        value: 150,
                        child: {
                            "手机": "50",
                            "电脑": "40",
                            "平板": "30",
                            "其他": "30"
                        }
                    },
                    {
                        name: "服装鞋帽",
                        value: 120,
                        child: {
                            "上衣": "40",
                            "裤子": "35",
                            "鞋子": "25",
                            "配饰": "20"
                        }
                    },
                    {
                        name: "家居用品",
                        value: 100,
                        child: {
                            "家具": "40",
                            "装饰": "30",
                            "厨具": "20",
                            "其他": "10"
                        }
                    }
                ],
                dimsCodes: ["主分类", "子分类"]
            },
            
            // 普通饼图节点
            normalPieNode: {
                id: 'normal-pie-test',
                code: 'PieChartWidget',
                widget: JSON.stringify({
                    beforeData: JSON.stringify({
                        xData: [{ label: '数值', alias: '数值', data: { code: 'value' }, func: { name: '求和' } }],
                        yData: [{ label: '类别', alias: '类别' }],
                        style: { title: { show: true, text: '普通饼图测试' }, legend: { legend_pos: "top" } }
                    })
                })
            },
            
            // 环形饼图节点
            ringPieNode: {
                id: 'ring-pie-test',
                code: 'RingPieChartWidget',
                widget: JSON.stringify({
                    beforeData: JSON.stringify({
                        xData: [{ label: 'value', alias: '销量', data: { code: 'value' }, func: { name: '求和' } }],
                        yData: [
                            { label: 'category', alias: '主分类' },
                            { label: 'subcategory', alias: '子分类' }
                        ],
                        style: { title: { show: true, text: '环形饼图测试' }, legend: { legend_pos: "top" } }
                    })
                })
            },
            
            // 环形饼图单维度节点
            ringPieSingleNode: {
                id: 'ring-pie-single-test',
                code: 'RingPieChartWidget',
                widget: JSON.stringify({
                    beforeData: JSON.stringify({
                        xData: [{ label: 'value', alias: '数值', data: { code: 'value' }, func: { name: '求和' } }],
                        yData: [{ label: 'category', alias: '类别' }],
                        style: { title: { show: true, text: '环形饼图-单维度' }, legend: { legend_pos: "top" } }
                    })
                })
            },
            
            // 环形饼图多维度节点
            ringPieMultiNode: {
                id: 'ring-pie-multi-test',
                code: 'RingPieChartWidget',
                widget: JSON.stringify({
                    beforeData: JSON.stringify({
                        xData: [{ label: 'value', alias: '销量', data: { code: 'value' }, func: { name: '求和' } }],
                        yData: [
                            { label: 'category', alias: '主分类' },
                            { label: 'subcategory', alias: '子分类' }
                        ],
                        style: { title: { show: true, text: '环形饼图-多维度' }, legend: { legend_pos: "top" } }
                    })
                })
            }
        }
    },
    
    mounted() {
        this.loadTestData();
    },
    
    methods: {
        loadTestData() {
            setTimeout(() => {
                // 加载普通饼图数据
                this.sendDataToComponent('normal-pie-test', { status: 0, data: this.normalData });
                
                // 加载环形饼图数据
                this.sendDataToComponent('ring-pie-test', { status: 0, data: this.ringData });
                this.sendDataToComponent('ring-pie-single-test', { status: 0, data: this.normalData });
                this.sendDataToComponent('ring-pie-multi-test', { status: 0, data: this.ringData });
            }, 500);
        },
        
        sendDataToComponent(componentId, data) {
            const component = this.findPieCardComponent(componentId);
            if (component) {
                component.getMessage({ data: JSON.stringify(data) });
            }
        },
        
        findPieCardComponent(id) {
            const findComponent = (children) => {
                for (let child of children) {
                    if (child.$options.name === 'PieCard' && child.node && child.node.id === id) {
                        return child;
                    }
                    if (child.$children && child.$children.length > 0) {
                        const found = findComponent(child.$children);
                        if (found) return found;
                    }
                }
                return null;
            };
            return findComponent(this.$children);
        }
    }
}
</script>

<style scoped lang="less">
.ring-pie-test {
    padding: 20px;
    
    h2 {
        color: #303133;
        margin-bottom: 30px;
        text-align: center;
        border-bottom: 2px solid #409eff;
        padding-bottom: 10px;
    }
    
    .test-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 30px;
        
        .test-item {
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            padding: 15px;
            background-color: #fff;
            
            h3 {
                color: #606266;
                margin-bottom: 10px;
                font-size: 14px;
                text-align: center;
                background-color: #f5f7fa;
                padding: 8px;
                border-radius: 4px;
            }
            
            .test-description {
                font-size: 12px;
                margin-bottom: 15px;
                padding: 8px;
                border-radius: 4px;
                text-align: center;
                
                &:contains("✅") {
                    background-color: #f0f9ff;
                    color: #409eff;
                }
                
                &:contains("❌") {
                    background-color: #fef0f0;
                    color: #f56c6c;
                }
            }
            
            .chart-container {
                width: 100%;
                height: 300px;
                position: relative;
                border: 1px solid #e4e7ed;
                border-radius: 4px;
            }
        }
    }
    
    .feature-description {
        background-color: #f0f9ff;
        padding: 20px;
        border-radius: 8px;
        border-left: 4px solid #409eff;
        margin-bottom: 20px;
        
        h3 {
            color: #409eff;
            margin-bottom: 15px;
        }
        
        ul {
            margin: 0;
            padding-left: 20px;
            
            li {
                color: #606266;
                margin-bottom: 8px;
                line-height: 1.5;
                
                strong {
                    color: #303133;
                }
                
                code {
                    background-color: #f5f7fa;
                    padding: 2px 4px;
                    border-radius: 2px;
                    font-size: 12px;
                }
            }
        }
    }
    
    .code-example {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        border-left: 4px solid #67c23a;
        
        h3 {
            color: #67c23a;
            margin-bottom: 15px;
        }
        
        pre {
            background-color: #fff;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            border: 1px solid #e4e7ed;
            
            code {
                font-size: 12px;
                color: #606266;
                line-height: 1.5;
            }
        }
    }
}

@media (max-width: 768px) {
    .test-grid {
        grid-template-columns: 1fr;
    }
}
</style>
