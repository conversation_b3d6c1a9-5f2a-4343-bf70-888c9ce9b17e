<template>
    <div class="pie-card-demo">
        <h2>饼图转表格功能演示</h2>
        <div class="demo-container">
            <PieCard 
                :node="mockNode" 
                :id="'demo-pie-card'"
                :isView="false"
            />
        </div>
        
        <div class="demo-description">
            <h3>功能说明：</h3>
            <ul>
                <li>点击右上角的切换按钮可以在饼图和表格之间切换</li>
                <li>表格显示与饼图相同的数据，包含颜色指示器和百分比进度条</li>
                <li>数据按数值大小降序排序</li>
                <li>支持数字格式化和响应式设计</li>
            </ul>
            
            <h3>测试数据格式：</h3>
            <pre>{{ JSON.stringify(testData, null, 2) }}</pre>
        </div>
    </div>
</template>

<script>
import PieCard from './PieCard.vue'

export default {
    name: 'PieCardDemo',
    components: {
        PieCard
    },
    data() {
        return {
            // 测试数据
            testData: {
                columns: ["民族名称", "年龄段"],
                rows: [
                    { "民族名称": "回族", "年龄段": 1 },
                    { "民族名称": "满族", "年龄段": 2 },
                    { "民族名称": "汉族", "年龄段": 92 },
                    { "民族名称": "蒙古族", "年龄段": 5 },
                    { "民族名称": "藏族", "年龄段": 3 }
                ],
                dimsCodes: ["民族名称"],
                formatMapping: {}
            },
            // 模拟节点数据
            mockNode: {
                id: 'demo-pie-card',
                code: 'PieChartWidget',
                widget: JSON.stringify({
                    beforeData: JSON.stringify({
                        xData: [{
                            label: '年龄段',
                            alias: '人数',
                            data: {
                                code: 'age_count',
                                numberFormat: '#,##0'
                            },
                            func: {
                                name: '求和'
                            }
                        }],
                        yData: [{
                            label: '民族名称',
                            alias: '民族'
                        }],
                        style: {
                            title: {
                                show: true,
                                text: '民族人数分布'
                            },
                            legend: {
                                color_type: "custom",
                                color_theme: "default",
                                color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'],
                                label_show: true,
                                label_pos: "outside",
                                label_cont: "dimension,percent"
                            }
                        }
                    })
                })
            }
        }
    },
    mounted() {
        // 模拟数据加载
        this.simulateDataLoad();
    },
    methods: {
        /**
         * 模拟数据加载
         */
        simulateDataLoad() {
            setTimeout(() => {
                // 使用实际数据格式
                const mockData = {
                    status: 0,
                    data: this.testData
                };
                
                // 触发数据更新
                const pieCardComponent = this.$children.find(child => child.$options.name === 'PieCard');
                if (pieCardComponent) {
                    pieCardComponent.getMessage({
                        data: JSON.stringify(mockData)
                    });
                }
            }, 1000);
        }
    }
}
</script>

<style scoped lang="less">
.pie-card-demo {
    padding: 20px;
    
    h2 {
        color: #303133;
        margin-bottom: 20px;
        text-align: center;
    }
    
    .demo-container {
        width: 100%;
        height: 400px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        margin-bottom: 20px;
        position: relative;
    }
    
    .demo-description {
        background-color: #f5f7fa;
        padding: 15px;
        border-radius: 4px;
        
        h3 {
            color: #606266;
            margin-bottom: 10px;
        }
        
        ul {
            margin: 0 0 15px 0;
            padding-left: 20px;
            
            li {
                color: #909399;
                margin-bottom: 5px;
                line-height: 1.5;
            }
        }
        
        pre {
            background-color: #f4f4f5;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            overflow-x: auto;
            color: #606266;
        }
    }
}
</style>
